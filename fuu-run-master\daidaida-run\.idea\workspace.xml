<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6d8e4c51-87ea-4b4b-838c-3bab59fc0568" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2wFV5IccZTIYV1EYHZYb4KlwOrH" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.daidaida-run [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.daidaida-run [package].executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-Database detector&quot;: &quot;数据库检测器&quot;,
    &quot;Notification.DoNotAsk-Database detector&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.DaidaidaAdminApplication.executor&quot;: &quot;Run&quot;,
    &quot;deletionFromPopupRequiresConfirmation&quot;: &quot;false&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/zy/yootk&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.itangcent.idea.plugin.configurable.AccountConfigurable.apifox&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.DaidaidaAdminApplication.executor&quot;: &quot;Run&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RunManager" selected="Spring Boot.DaidaidaAdminApplication">
    <configuration name="DaidaidaAdminApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.karrecy.DaidaidaAdminApplication" />
      <module name="daidaida-admin" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.karrecy.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DaidaidaAdminApplication" type="Application" factoryName="Application" temporary="true">
      <option name="MAIN_CLASS_NAME" value="com.karrecy.DaidaidaAdminApplication" />
      <module name="daidaida-admin" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.karrecy.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DaidaidaAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="daidaida-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.karrecy.DaidaidaAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.DaidaidaAdminApplication" />
        <item itemvalue="应用程序.DaidaidaAdminApplication" />
        <item itemvalue="应用程序.DaidaidaAdminApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="6d8e4c51-87ea-4b4b-838c-3bab59fc0568" name="更改" comment="" />
      <created>1745637124107</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745637124107</updated>
      <workItem from="1745637125195" duration="153000" />
      <workItem from="1745637354931" duration="1258000" />
      <workItem from="1745638628889" duration="8339000" />
      <workItem from="1745720127468" duration="8461000" />
      <workItem from="1745735913625" duration="11583000" />
      <workItem from="1745892385558" duration="378000" />
      <workItem from="1745892957350" duration="5371000" />
      <workItem from="1746496950928" duration="3271000" />
      <workItem from="1746516681413" duration="128000" />
      <workItem from="1746519025117" duration="659000" />
      <workItem from="1746751585550" duration="1211000" />
      <workItem from="1747106039305" duration="59000" />
      <workItem from="1747107830614" duration="137000" />
      <workItem from="1747120799914" duration="2686000" />
      <workItem from="1747354495216" duration="3103000" />
      <workItem from="1747362022681" duration="1827000" />
      <workItem from="1747369918558" duration="29651000" />
      <workItem from="1747403669921" duration="3942000" />
      <workItem from="1747407648065" duration="8978000" />
      <workItem from="1747448109291" duration="6006000" />
      <workItem from="1747456465772" duration="5415000" />
      <workItem from="1747465830399" duration="7900000" />
      <workItem from="1747476517143" duration="4306000" />
      <workItem from="1747483351298" duration="4476000" />
      <workItem from="1747709131946" duration="2732000" />
      <workItem from="1747880223721" duration="158000" />
      <workItem from="1747883284864" duration="715000" />
      <workItem from="1747901469272" duration="128000" />
      <workItem from="1748090752282" duration="10331000" />
      <workItem from="1748315854941" duration="11000" />
      <workItem from="1748315975873" duration="8000" />
      <workItem from="1748338568589" duration="2207000" />
      <workItem from="1748344460996" duration="5256000" />
      <workItem from="1748413469438" duration="11414000" />
      <workItem from="1748513775585" duration="5821000" />
      <workItem from="1748614774223" duration="1366000" />
      <workItem from="1749468354781" duration="39000" />
      <workItem from="1749712657273" duration="141000" />
      <workItem from="1749972631164" duration="6976000" />
      <workItem from="1749985738983" duration="367000" />
      <workItem from="1750746086899" duration="1177000" />
      <workItem from="1750918044966" duration="244000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="com.github.binarywang.wxpay.exception.WxPayException" package="com.github.binarywang.wxpay.exception" />
          <option name="timeStamp" value="1" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>