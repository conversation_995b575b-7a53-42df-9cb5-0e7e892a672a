<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.karrecy</groupId>
    <artifactId>daidaida-run</artifactId>
    <version>1.0.0</version>
  </parent>
  <groupId>com.karrecy</groupId>
  <artifactId>daidaida-admin</artifactId>
  <version>1.0.0</version>
  <description>服务入口</description>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>
    <dependency>
      <groupId>com.karrecy</groupId>
      <artifactId>daidaida-framework</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.karrecy</groupId>
      <artifactId>daidaida-system</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.karrecy</groupId>
      <artifactId>daidaida-order</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.karrecy</groupId>
      <artifactId>daidaida-payment</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <fork>true</fork>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <version>3.2.2</version>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <warName>${project.artifactId}</warName>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
