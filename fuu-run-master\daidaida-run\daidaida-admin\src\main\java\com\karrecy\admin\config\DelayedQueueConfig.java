package com.karrecy.admin.config;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.karrecy.common.constant.QueueNames;
import com.karrecy.common.constant.UserConstants;
import com.karrecy.common.enums.OrderStatus;
import com.karrecy.common.utils.redis.QueueUtils;
import com.karrecy.order.domain.po.OrderMain;
import com.karrecy.order.service.IOrderMainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 延迟队列配置类
 * 在应用启动时自动启动延迟队列监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DelayedQueueConfig implements ApplicationRunner {

    private final IOrderMainService orderMainService;

    // 标记监听器是否已启动，避免重复启动
    private volatile boolean listenerStarted = false;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 延迟启动监听器，确保所有Bean都已初始化
        new Thread(() -> {
            try {
                Thread.sleep(5000); // 等待5秒确保系统完全启动
                startDelayedQueueListener();
            } catch (InterruptedException e) {
                log.error("延迟队列监听器启动线程被中断", e);
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    /**
     * 启动延迟队列监听器
     */
    public synchronized void startDelayedQueueListener() {
        if (listenerStarted) {
            log.warn("延迟队列监听器已经启动，跳过重复启动");
            return;
        }

        try {
            String queueName = QueueNames.ORDER_DELIVERED_AUTO_COMPLETE;
            log.info("应用启动时自动启动延迟队列监听器: {}", queueName);

            // 使用直接的Redisson API来确保监听器正确工作
            startDirectDelayedQueueListener(queueName);

            listenerStarted = true;
            log.info("延迟队列监听器启动成功: {}", queueName);

        } catch (Exception e) {
            log.error("启动延迟队列监听器失败", e);
        }
    }

    /**
     * 使用直接的Redisson API启动延迟队列监听器
     */
    private void startDirectDelayedQueueListener(String queueName) {
        try {
            // 直接使用Redisson API
            RedissonClient client = QueueUtils.getClient();
            RBlockingQueue<String> queue = client.getBlockingQueue(queueName);
            RDelayedQueue<String> delayedQueue = client.getDelayedQueue(queue);

            // 启动一个后台线程来监听队列
            Thread listenerThread = new Thread(() -> {
                log.info("延迟队列监听线程启动: {}", queueName);
                while (!Thread.currentThread().isInterrupted()) {
                    try {
                        // 阻塞等待队列中的消息
                        String orderNum = queue.take();
                        log.info("延迟队列收到订单数据: {}", orderNum);

                        // 检查是否是测试订单
                        if (orderNum.startsWith("TEST_ORDER_") || orderNum.startsWith("LISTENER_TEST_")) {
                            log.info("收到测试订单，延迟队列工作正常: {}", orderNum);
                            continue;
                        }

                        // 处理真实订单
                        processOrderAutoComplete(orderNum);

                    } catch (InterruptedException e) {
                        log.warn("延迟队列监听线程被中断: {}", e.getMessage());
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        log.error("处理延迟队列消息异常: {}", e.getMessage(), e);
                    }
                }
                log.info("延迟队列监听线程结束: {}", queueName);
            });

            listenerThread.setName("DelayedQueue-Listener-" + queueName);
            listenerThread.setDaemon(true); // 设置为守护线程
            listenerThread.start();

            log.info("延迟队列监听线程已启动: {}", queueName);

        } catch (Exception e) {
            log.error("启动延迟队列监听线程失败", e);
            throw e;
        }
    }

    /**
     * 处理订单自动完成
     */
    private void processOrderAutoComplete(String orderNum) {
        try {
            log.info("处理订单送达未完成 自动完成，订单号: {}", orderNum);
            Long orderId = Long.valueOf(orderNum);
            OrderMain orderMainDB = orderMainService.getById(orderId);

            if (orderMainDB == null) {
                log.warn("订单不存在，订单ID: {}", orderId);
                return;
            }

            if (!ObjectUtil.equals(orderMainDB.getStatus(), OrderStatus.DELIVERED.getCode())) {
                log.info("订单状态已变更，不需自动完成，订单ID: {}, 当前状态: {}", orderId, orderMainDB.getStatus());
                return;
            }

            log.info("开始自动确认订单完成，订单ID: {}", orderId);

            // 直接调用系统自动确认方法，无需权限检查
            orderMainService.autoConfirmBySystem(orderId);
            log.info("自动确认订单完成成功，订单ID: {}", orderId);

        } catch (Exception e) {
            log.error("处理订单自动完成失败，订单号: {}, 错误: {}", orderNum, e.getMessage(), e);
        }
    }


}
