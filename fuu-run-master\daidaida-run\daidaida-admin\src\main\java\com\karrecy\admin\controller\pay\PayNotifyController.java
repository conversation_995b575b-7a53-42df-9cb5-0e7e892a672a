package com.karrecy.admin.controller.pay;



import cn.dev33.satoken.annotation.SaIgnore;
import com.karrecy.common.constant.QueueNames;
import com.karrecy.common.core.domain.R;
import com.karrecy.common.utils.TimeZoneUtils;
import com.karrecy.common.utils.redis.QueueUtils;
import com.karrecy.payment.service.IPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.karrecy.common.exception.PayException;
import com.karrecy.common.helper.LoginHelper;
import com.karrecy.payment.domain.vo.PayedVO;
import com.karrecy.system.mapper.UserWxMapper;
import com.karrecy.common.core.domain.entity.UserWx;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 支付回调相关接口
 */
@RestController
@RequestMapping("/notify")
@Slf4j
@RequiredArgsConstructor
public class PayNotifyController {

    private final IPayService payService;
    private final UserWxMapper userWxMapper;

    /**
     * 支付成功回调
     */
    @SaIgnore
    @PostMapping("/payNotify")
    public String paySuccessNotify(@RequestBody String notifyData, HttpServletRequest request) {
        log.info("接收到支付回调请求，时间: {}, IP地址: {}", TimeZoneUtils.getNowString(), request.getRemoteAddr());
        try {
            String result = payService.payNotifyV3(notifyData, request);
            log.info("支付回调处理完成，响应结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("支付回调处理异常: {}", e.getMessage(), e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>";
        }
    }

    /**
     * 退款成功回调
     */
    @SaIgnore
    @PostMapping("/refundNotify")
    public String refundNotify(@RequestBody String notifyData, HttpServletRequest request) {
        log.info("接收到退款回调请求，时间: {}, IP地址: {}", TimeZoneUtils.getNowString(), request.getRemoteAddr());
        try {
            String result = payService.refundNotify(notifyData, request);
            log.info("退款回调处理完成，响应结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("退款回调处理异常: {}", e.getMessage(), e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>";
        }
    }

    /**
     * 模拟支付成功
     * 用于测试环境下，手动触发支付成功流程
     */
    @SaIgnore
    @GetMapping("/mockPaySuccess/{orderId}")
    public R<Void> mockPaySuccess(@PathVariable String orderId) {
        log.info("模拟支付成功，订单ID: {}, 时间: {}", orderId, TimeZoneUtils.getNowString());
        try {
            // 将订单ID添加到支付成功队列中
            QueueUtils.addQueueObject(QueueNames.ORDER_PAY_SUCCESS, orderId);
            log.info("已将订单ID添加到支付成功队列: {}", orderId);
            return R.ok();
        } catch (Exception e) {
            log.error("模拟支付成功处理异常: {}", e.getMessage(), e);
            return R.fail("处理异常");
        }
    }
    
    /**
     * 手动修复订单支付状态
     * 用于处理支付成功但状态未更新的订单
     */
    @SaIgnore
    @GetMapping("/fixOrderPayStatus/{orderId}")
    public R<Void> fixOrderPayStatus(@PathVariable String orderId) {
        log.info("手动修复订单支付状态，订单ID: {}, 时间: {}", orderId, TimeZoneUtils.getNowString());
        try {
            // 将订单ID添加到支付成功队列中以触发状态更新
            QueueUtils.addQueueObject(QueueNames.ORDER_PAY_SUCCESS, orderId);
            log.info("已将订单ID添加到支付成功队列以修复状态: {}", orderId);
            return R.ok("修复指令已发送，请稍后查看订单状态");
        } catch (Exception e) {
            log.error("修复订单支付状态异常: {}", e.getMessage(), e);
            return R.fail("处理异常: " + e.getMessage());
        }
    }

    /**
     * 申诉补差价支付接口
     * 专门处理已申诉订单的补差价支付请求，忽略订单状态检查
     * 
     * @param requestMap 包含订单ID和补差价金额的请求参数
     * @return 支付参数
     */
    @SaIgnore
    @PostMapping("/appeal/extra")
    public R<PayedVO> appealExtraPayment(@RequestBody Map<String, Object> requestMap) {
        log.info("收到补差价支付请求: {}", requestMap);
        
        try {
            // 获取参数
            Object orderIdObj = requestMap.get("orderId");
            Object amountObj = requestMap.get("amount");
            
            if (orderIdObj == null) {
                return R.fail("订单ID不能为空");
            }
            
            if (amountObj == null) {
                return R.fail("补差价金额不能为空");
            }
            
            // 转换参数类型
            Long orderId;
            BigDecimal amount;
            
            try {
                orderId = Long.valueOf(orderIdObj.toString());
                amount = new BigDecimal(amountObj.toString());
            } catch (NumberFormatException e) {
                log.error("参数格式错误", e);
                return R.fail("参数格式错误");
            }
            
            // 验证金额
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return R.fail("补差价金额必须大于0");
            }
            
            // 获取当前登录用户的openid
            Long userId = LoginHelper.getUserId();
            UserWx userWx = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>()
                    .eq(UserWx::getUid, userId));
            
            if (userWx == null || userWx.getOpenid() == null) {
                return R.fail("未找到用户微信信息");
            }
            
            // 构建支付描述
            String desc = "订单补差价-" + orderId;
            
            // 调用支付服务，忽略订单状态检查
            PayedVO payResult = payService.payExtraAmount(desc, orderId, amount, userWx.getOpenid());
            
            log.info("补差价支付参数生成成功: orderId={}, amount={}, openid={}", orderId, amount, userWx.getOpenid());
            
            return R.ok(payResult);
        } catch (PayException e) {
            log.error("补差价支付请求处理异常", e);
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("补差价支付请求处理异常", e);
            return R.fail("处理补差价支付请求失败: " + e.getMessage());
        }
    }
}
