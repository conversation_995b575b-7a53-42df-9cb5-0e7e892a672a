{"doc": " 延迟队列配置类\n 在应用启动时自动启动延迟队列监听器\n", "fields": [], "enumConstants": [], "methods": [{"name": "startDelayedQueueListener", "paramTypes": [], "doc": " 启动延迟队列监听器\n"}, {"name": "startDirectDelayedQueueListener", "paramTypes": ["java.lang.String"], "doc": " 使用直接的Redisson API启动延迟队列监听器\n"}, {"name": "processOrderAutoComplete", "paramTypes": ["java.lang.String"], "doc": " 处理订单自动完成\n"}], "constructors": []}