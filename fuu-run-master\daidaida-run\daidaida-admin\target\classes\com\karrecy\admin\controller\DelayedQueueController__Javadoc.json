{"doc": " 延迟队列 演示案例\n <p>\n 轻量级队列 重量级数据量 请使用 MQ\n 例如: 创建订单30分钟后过期处理\n <p>\n 集群测试通过 同一个数据只会被消费一次 做好事务补偿\n 集群测试流程 两台集群分别开启订阅 在其中一台发送数据 观察接收消息的规律\n\n <AUTHOR> Li\n @version 3.6.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "subscribe", "paramTypes": ["java.lang.String"], "doc": " 订阅队列\n\n @param queueName 队列名\n"}, {"name": "add", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 添加队列数据\n\n @param queueName 队列名\n @param orderNum  订单号\n @param time      延迟时间(秒)\n"}, {"name": "remove", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 删除队列数据\n\n @param queueName 队列名\n @param orderNum  订单号\n"}, {"name": "destroy", "paramTypes": ["java.lang.String"], "doc": " 销毁队列\n\n @param queueName 队列名\n"}, {"name": "diagnoseDeliveredOrders", "paramTypes": [], "doc": " 诊断已送达但未自动完成的订单\n"}, {"name": "triggerAutoComplete", "paramTypes": ["java.lang.Long"], "doc": " 手动触发订单自动完成\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " 测试延迟队列是否正常工作\n"}, {"name": "fixOverdueOrders", "paramTypes": [], "doc": " 批量修复超时未自动完成的订单\n"}, {"name": "debugQueueStatus", "paramTypes": [], "doc": " 检查Redis延迟队列状态\n"}, {"name": "diagnoseListenerStatus", "paramTypes": [], "doc": " 诊断延迟队列监听器状态\n"}, {"name": "subscribeDelayedQ<PERSON>ue", "paramTypes": [], "doc": " 正确订阅延迟队列监听器\n"}, {"name": "subscribeDirectDelayedQueue", "paramTypes": [], "doc": " 直接使用Redisson API订阅延迟队列\n"}, {"name": "processOrderAutoComplete", "paramTypes": ["java.lang.String"], "doc": " 处理订单自动完成\n"}, {"name": "testSystemConfirm", "paramTypes": ["java.lang.Long"], "doc": " 测试系统自动确认订单功能\n"}, {"name": "restartDelayedQueueListener", "paramTypes": [], "doc": " 重启延迟队列监听器\n"}, {"name": "testListenerWorking", "paramTypes": [], "doc": " 检查延迟队列监听器状态并测试\n"}], "constructors": []}