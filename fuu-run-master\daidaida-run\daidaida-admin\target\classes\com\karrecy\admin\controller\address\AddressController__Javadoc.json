{"doc": " 地址控制器\n 处理应用程序的地址相关操作\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "add", "paramTypes": ["com.karrecy.order.domain.po.Address"], "doc": " 新增用户地址\n @param address 包含地址详细信息的地址对象\n @return 表示成功或失败的响应\n"}, {"name": "edit", "paramTypes": ["com.karrecy.order.domain.po.Address"], "doc": " 更新用户地址\n @param address 包含更新后地址详细信息的地址对象\n @return 表示成功或失败的响应\n"}, {"name": "list", "paramTypes": ["com.karrecy.order.domain.po.Address", "com.karrecy.common.core.domain.PageQuery"], "doc": " 分页查询地址\n @param address 包含查询参数的地址对象\n @param pageQuery 包含分页详细信息的分页查询对象\n @return 分页后的地址列表\n"}, {"name": "listCurr", "paramTypes": ["com.karrecy.common.core.domain.PageQuery"], "doc": " 获取当前用户的地址列表\n @param pageQuery 包含分页详细信息的分页查询对象\n @return 当前用户的地址列表\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 根据地址ID删除地址\n @param addressIds 要删除的地址ID数组\n @return 表示成功或失败的响应\n"}, {"name": "removeById", "paramTypes": ["java.lang.Long"], "doc": " 根据地址ID删除当前用户的地址\n @param addressId 要删除的地址ID\n @return 表示成功或失败的响应\n"}, {"name": "getById", "paramTypes": ["java.lang.Long"], "doc": " 根据地址ID获取当前用户的地址\n @param addressId 要获取的地址ID\n @return 包含地址详细信息的地址对象\n"}], "constructors": []}