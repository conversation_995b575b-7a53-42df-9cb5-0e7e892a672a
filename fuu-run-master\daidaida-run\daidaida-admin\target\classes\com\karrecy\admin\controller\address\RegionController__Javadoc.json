{"doc": " 校区区域控制器\n 处理应用程序的校区区域相关操作\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "add", "paramTypes": ["com.karrecy.order.domain.po.SchoolRegion"], "doc": " 新增校区区域\n @param schoolRegion 包含校区区域详细信息的校区区域对象\n @return 表示成功或失败的响应\n"}, {"name": "edit", "paramTypes": ["com.karrecy.order.domain.po.SchoolRegion"], "doc": " 修改校区区域\n @param schoolRegion 包含更新后校区区域详细信息的校区区域对象\n @return 表示成功或失败的响应\n"}, {"name": "list", "paramTypes": ["java.lang.Long"], "doc": " 用户校区区域分页查询\n @param schoolId 校区ID\n @return 分页后的校区区域列表\n"}, {"name": "listUser", "paramTypes": ["com.karrecy.order.domain.po.SchoolRegion"], "doc": " 校区区域列表查询\n @param schoolRegion 包含查询参数的校区区域对象\n @return 校区区域列表\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除校区区域\n @param schoolRegionIds 要删除的校区区域ID数组\n @return 表示成功或失败的响应\n"}], "constructors": []}