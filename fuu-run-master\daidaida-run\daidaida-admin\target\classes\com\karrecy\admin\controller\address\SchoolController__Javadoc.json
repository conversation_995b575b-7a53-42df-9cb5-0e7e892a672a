{"doc": " 校区控制器\n 处理应用程序的校区相关操作\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "add", "paramTypes": ["com.karrecy.order.domain.po.School"], "doc": " 新增校区\n @param school 包含校区详细信息的校区对象\n @return 表示成功或失败的响应\n"}, {"name": "edit", "paramTypes": ["com.karrecy.order.domain.po.School"], "doc": " 修改校区\n @param school 包含更新后校区详细信息的校区对象\n @return 表示成功或失败的响应\n"}, {"name": "list", "paramTypes": ["com.karrecy.order.domain.po.School", "com.karrecy.common.core.domain.PageQuery"], "doc": " 校区分页查询\n @param school 包含查询参数的校区对象\n @param pageQuery 包含分页详细信息的分页查询对象\n @return 分页后的校区列表\n"}, {"name": "get", "paramTypes": ["java.lang.Long"], "doc": " 根据校区ID获取校区\n @param id 要获取的校区ID\n @return 包含校区详细信息的校区对象\n"}], "constructors": []}