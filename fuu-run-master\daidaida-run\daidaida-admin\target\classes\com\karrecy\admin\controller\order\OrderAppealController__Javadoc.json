{"doc": " 订单申诉控制器\n 处理应用程序的订单申诉相关操作\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["com.karrecy.order.domain.po.OrderAppeal", "com.karrecy.common.core.domain.PageQuery"], "doc": " 申诉分页查询\n @param orderAppeal 包含查询参数的订单申诉对象\n @param pageQuery 包含分页详细信息的分页查询对象\n @return 分页后的订单申诉列表\n"}, {"name": "getAppeal", "paramTypes": ["java.lang.Long"], "doc": " 根据orderId查询申诉\n @param orderId 要查询的订单ID\n @return 包含订单申诉详细信息的订单申诉对象列表\n"}, {"name": "submitAppeal", "paramTypes": ["com.karrecy.order.domain.dto.OrderAppealDTO"], "doc": " 提交申诉\n @param orderAppealDTO 包含申诉详细信息的订单申诉数据传输对象\n @return 表示成功或失败的响应\n"}, {"name": "edit", "paramTypes": ["com.karrecy.order.domain.po.OrderAppeal"], "doc": " 处理申诉\n @param orderAppeal 包含处理后申诉详细信息的订单申诉对象\n @return 表示成功或失败的响应\n"}, {"name": "confirmComplete", "paramTypes": ["java.lang.Long"], "doc": " 确认补差价订单完成\n 将已申诉且已支付补差价的订单状态改为已完成\n \n @param orderId 订单ID\n @return 表示成功或失败的响应\n"}], "constructors": []}