{"doc": " 订单聊天控制器\n 处理应用程序的订单聊天相关操作\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "pageChat", "paramTypes": ["java.lang.Long", "com.karrecy.common.core.domain.PageQuery"], "doc": " 订单聊天记录分页查询\n @param orderId 订单ID\n @param pageQuery 包含分页详细信息的分页查询对象\n @return 分页后的订单聊天记录列表\n"}, {"name": "initchat", "paramTypes": ["java.lang.Long"], "doc": " 获取聊天展示信息\n @param orderId 订单ID\n @return 包含聊天展示信息的响应\n"}], "constructors": []}