{"doc": " 订单控制器\n 处理应用程序的订单相关操作\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "add", "paramTypes": ["com.karrecy.order.domain.dto.OrderSubmitDTO"], "doc": " 新建订单\n @param orderSubmitDTO 包含订单详细信息的订单提交数据传输对象\n @return 表示成功或失败的响应\n"}, {"name": "payAgain", "paramTypes": ["java.lang.Long"], "doc": " 继续支付\n @param orderId 订单ID\n @return 表示成功或失败的响应\n"}, {"name": "refund", "paramTypes": ["java.lang.Long", "java.math.BigDecimal"], "doc": " 订单退款\n @param orderId 订单ID\n @param amount 退款金额\n @return 表示成功或失败的响应\n"}, {"name": "cancel", "paramTypes": ["com.karrecy.order.domain.dto.OrderCancelDTO"], "doc": " 取消订单\n @param orderCancelDTO 包含取消订单详细信息的订单取消数据传输对象\n @return 表示成功或失败的响应\n"}, {"name": "cancelbefore", "paramTypes": ["java.lang.Long"], "doc": " 取消订单前置操作\n @param orderId 订单ID\n @return 表示成功或失败的响应\n"}, {"name": "list", "paramTypes": ["com.karrecy.order.domain.dto.OrderQuery", "com.karrecy.common.core.domain.PageQuery"], "doc": " 大厅订单查询\n @param orderQuery 包含查询参数的订单查询对象\n @param pageQuery 包含分页详细信息的分页查询对象\n @return 分页后的订单列表\n"}, {"name": "listUser", "paramTypes": ["com.karrecy.order.domain.dto.OrderQuery", "com.karrecy.common.core.domain.PageQuery"], "doc": " 我的订单查询\n @param orderQuery 包含查询参数的订单查询对象\n @param pageQuery 包含分页详细信息的分页查询对象\n @return 分页后的订单列表\n"}, {"name": "detail", "paramTypes": ["java.lang.Long"], "doc": " 订单详情查询\n @param orderId 订单ID\n @return 包含订单详细信息的订单详情对象\n"}, {"name": "accept", "paramTypes": ["java.lang.Long"], "doc": " 跑腿员接单\n @param orderId 订单ID\n @return 表示成功或失败的响应\n"}, {"name": "delivery", "paramTypes": ["java.lang.Long"], "doc": " 跑腿员配送\n @param orderId 订单ID\n @return 表示成功或失败的响应\n"}, {"name": "complete", "paramTypes": ["com.karrecy.order.domain.dto.OrderCompleteDTO"], "doc": " 完成订单\n @param orderCompleteDTO 包含完成订单详细信息的订单完成数据传输对象\n @return 表示成功或失败的响应\n"}, {"name": "suppleImages", "paramTypes": ["com.karrecy.order.domain.dto.OrderCompleteDTO"], "doc": " 补充凭证\n @param orderCompleteDTO 包含补充凭证详细信息的订单完成数据传输对象\n @return 表示成功或失败的响应\n"}, {"name": "confirm", "paramTypes": ["java.lang.Long"], "doc": " 确定送达\n @param orderId 订单ID\n @return 表示成功或失败的响应\n"}, {"name": "phone", "paramTypes": ["java.lang.Long"], "doc": " 获取用户/跑腿员电话\n @param orderId 订单ID\n @return 包含用户/跑腿员电话的响应\n"}], "constructors": []}