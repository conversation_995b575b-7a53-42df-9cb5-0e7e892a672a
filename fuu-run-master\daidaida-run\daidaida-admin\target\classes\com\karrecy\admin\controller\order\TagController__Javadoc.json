{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "add", "paramTypes": ["com.karrecy.order.domain.po.Tags"], "doc": " 新增tag\n @param tags\n @return\n"}, {"name": "edit", "paramTypes": ["com.karrecy.order.domain.po.Tags"], "doc": " 修改tag\n @param tags\n @return\n"}, {"name": "list", "paramTypes": ["com.karrecy.order.domain.po.Tags", "com.karrecy.common.core.domain.PageQuery"], "doc": " tag分页查询\n"}, {"name": "get", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据school和ordertype查询list\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除tags\n\n @param tagIds tagsID\n"}], "constructors": []}