{"doc": " 支付回调相关接口\n", "fields": [], "enumConstants": [], "methods": [{"name": "paySuccessNotify", "paramTypes": ["java.lang.String", "javax.servlet.http.HttpServletRequest"], "doc": " 支付成功回调\n"}, {"name": "refundNotify", "paramTypes": ["java.lang.String", "javax.servlet.http.HttpServletRequest"], "doc": " 退款成功回调\n"}, {"name": "mockPaySuccess", "paramTypes": ["java.lang.String"], "doc": " 模拟支付成功\n 用于测试环境下，手动触发支付成功流程\n"}, {"name": "fixOrderPayStatus", "paramTypes": ["java.lang.String"], "doc": " 手动修复订单支付状态\n 用于处理支付成功但状态未更新的订单\n"}, {"name": "appealExtraPayment", "paramTypes": ["java.util.Map"], "doc": " 申诉补差价支付接口\n 专门处理已申诉订单的补差价支付请求，忽略订单状态检查\n \n @param requestMap 包含订单ID和补差价金额的请求参数\n @return 支付参数\n"}], "constructors": []}