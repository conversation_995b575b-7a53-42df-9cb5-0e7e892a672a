{"doc": " @Description:\n 登录方法\n", "fields": [], "enumConstants": [], "methods": [{"name": "login", "paramTypes": ["com.karrecy.common.core.domain.model.LoginBody"], "doc": " 登录方法\n @param loginBody 登录信息\n @return 结果\n"}, {"name": "emaillogin", "paramTypes": ["com.karrecy.common.core.domain.model.EmailBody"], "doc": " 登录方法\n @param emailBody 登录信息\n @return 结果\n"}, {"name": "xcxLogin", "paramTypes": ["java.lang.String"], "doc": " 登录方法\n @param xcxCode 登录信息\n @return 结果\n"}, {"name": "xcxCheckLogin", "paramTypes": [], "doc": " 小程序检查是否需要登录\n @return 结果\n"}, {"name": "getInfo", "paramTypes": [], "doc": " 获取用户信息\n\n @return 用户信息\n"}, {"name": "updateCarouselImage", "paramTypes": ["int", "java.util.Map"], "doc": " 修改指定索引的轮播图图片\n 请求体应为 JSON 格式，例如：\n {\n   \"base64Content\": \"新的图片Base64编码，不包含前缀\"\n }\n"}], "constructors": []}