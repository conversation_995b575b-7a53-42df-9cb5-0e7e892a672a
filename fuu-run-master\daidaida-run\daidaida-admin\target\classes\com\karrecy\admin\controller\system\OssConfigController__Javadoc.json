{"doc": " 对象存储配置\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["com.karrecy.system.domain.bo.OssConfigBo", "com.karrecy.common.core.domain.PageQuery"], "doc": " 查询对象存储配置列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取对象存储配置详细信息\n\n @param ossConfigId OSS配置ID\n"}, {"name": "add", "paramTypes": ["com.karrecy.system.domain.bo.OssConfigBo"], "doc": " 新增对象存储配置\n"}, {"name": "edit", "paramTypes": ["com.karrecy.system.domain.bo.OssConfigBo"], "doc": " 修改对象存储配置\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除对象存储配置\n\n @param ossConfigIds OSS配置ID串\n"}, {"name": "changeStatus", "paramTypes": ["com.karrecy.system.domain.bo.OssConfigBo"], "doc": " 状态修改\n"}], "constructors": []}