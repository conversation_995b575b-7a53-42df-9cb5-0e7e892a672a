{"doc": " 文件上传 控制层\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["com.karrecy.system.domain.bo.OssBo", "com.karrecy.common.core.domain.PageQuery"], "doc": " 查询OSS对象存储列表\n"}, {"name": "upload", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Integer", "java.lang.String"], "doc": " 上传OSS对象存储\n\n @param file 文件\n"}, {"name": "download", "paramTypes": ["java.lang.Long", "javax.servlet.http.HttpServletResponse"], "doc": " 下载OSS对象\n\n @param ossId OSS对象ID\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除OSS对象存储\n\n @param ossIds OSS对象ID串\n"}], "constructors": []}