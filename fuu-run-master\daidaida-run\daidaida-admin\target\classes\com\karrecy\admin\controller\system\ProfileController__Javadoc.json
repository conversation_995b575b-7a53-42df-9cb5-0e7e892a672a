{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "updatePwd", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 修改密码\n"}, {"name": "bindEmail", "paramTypes": ["java.util.Map"], "doc": " 绑定邮箱\n"}, {"name": "sendEmailCode", "paramTypes": ["java.lang.String"], "doc": " 发送邮箱验证码\n"}, {"name": "updateProfile", "paramTypes": ["com.karrecy.system.domain.bo.ProfileUpdateDTO"], "doc": " 修改用户\n"}, {"name": "bindPhone", "paramTypes": ["java.lang.String"], "doc": " 绑定手机号\n"}, {"name": "canReqPhone", "paramTypes": [], "doc": " 是否显示手机号按钮\n"}], "constructors": []}