{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["com.karrecy.order.domain.po.RunnerApply", "com.karrecy.common.core.domain.PageQuery"], "doc": " 跑腿申请分页查询\n"}, {"name": "submit", "paramTypes": ["com.karrecy.order.domain.po.RunnerApply"], "doc": " 跑腿申请\n @param runnerApply\n @return\n"}, {"name": "edit", "paramTypes": ["com.karrecy.order.domain.po.RunnerApply"], "doc": " 处理申请\n @param runnerApply\n @return\n"}, {"name": "process", "paramTypes": [], "doc": " 查询自己的申请进度\n"}], "constructors": []}