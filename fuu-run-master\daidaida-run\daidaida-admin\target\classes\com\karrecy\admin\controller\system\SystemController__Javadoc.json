{"doc": " 对象存储配置\n", "fields": [], "enumConstants": [], "methods": [{"name": "monitor", "paramTypes": [], "doc": " 查询全局配置\n"}, {"name": "edit", "paramTypes": ["com.karrecy.common.config.DaidaidaConfig"], "doc": " 修改全局配置\n"}, {"name": "listPerms", "paramTypes": [], "doc": " 查询全部权限\n"}, {"name": "list", "paramTypes": ["java.lang.Integer"], "doc": " 根据角色查询权限\n"}, {"name": "roleperms", "paramTypes": ["java.lang.Integer", "java.util.List"], "doc": " 分配权限\n"}, {"name": "kickout", "paramTypes": ["java.lang.Integer"], "doc": " 踢下线，重新登录才能重新获取最新权限\n"}, {"name": "roleperms", "paramTypes": ["com.karrecy.common.core.domain.entity.Perm"], "doc": " 添加权限\n"}, {"name": "rolepermsDel", "paramTypes": ["java.util.List"], "doc": " 删除权限\n"}], "constructors": []}