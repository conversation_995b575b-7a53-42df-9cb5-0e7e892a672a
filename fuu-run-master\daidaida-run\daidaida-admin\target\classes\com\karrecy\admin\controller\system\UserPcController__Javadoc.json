{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "resetPwd", "paramTypes": ["java.lang.Long"], "doc": " 重设密码\n"}, {"name": "addUser", "paramTypes": ["com.karrecy.common.core.domain.entity.UserPc"], "doc": " 新增PC用户\n @param userPc\n @return\n"}, {"name": "editUser", "paramTypes": ["com.karrecy.common.core.domain.entity.UserPc"], "doc": " 修改pc用户\n @param userPc\n @return\n"}, {"name": "listPc", "paramTypes": ["com.karrecy.system.domain.bo.UserPcQuery", "com.karrecy.common.core.domain.PageQuery"], "doc": " 获取PC用户列表\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除用户\n\n @param uIds 角色ID串\n"}], "constructors": []}