package com.karrecy.common.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 时区工具类
 * 用于处理时间相关的问题，确保时间的准确性
 */
public class TimeZoneUtils {
    
    // 系统默认时区
    private static final ZoneId SYSTEM_ZONE = ZoneId.systemDefault();
    
    // 中国标准时区 (UTC+8)
    private static final ZoneId CHINA_ZONE = ZoneId.of("Asia/Shanghai");
    
    // 标准日期时间格式
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 获取当前时间（系统默认时区）
     */
    public static LocalDateTime getNow() {
        return LocalDateTime.now(SYSTEM_ZONE);
    }
    
    /**
     * 获取当前时间（中国标准时区）
     */
    public static LocalDateTime getNowInChina() {
        return LocalDateTime.now(CHINA_ZONE);
    }
    
    /**
     * 将LocalDateTime转换为指定时区
     */
    public static LocalDateTime convertToZone(LocalDateTime dateTime, ZoneId targetZone) {
        if (dateTime == null) {
            return null;
        }
        ZonedDateTime zonedDateTime = dateTime.atZone(SYSTEM_ZONE);
        return zonedDateTime.withZoneSameInstant(targetZone).toLocalDateTime();
    }
    
    /**
     * 将LocalDateTime转换为中国标准时区
     */
    public static LocalDateTime convertToChina(LocalDateTime dateTime) {
        return convertToZone(dateTime, CHINA_ZONE);
    }
    
    /**
     * 格式化LocalDateTime为标准字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATE_TIME_FORMATTER);
    }
    
    /**
     * 获取当前时间的格式化字符串（中国标准时区）
     */
    public static String getNowString() {
        return formatDateTime(getNowInChina());
    }
} 