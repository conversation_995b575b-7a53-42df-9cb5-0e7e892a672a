package com.karrecy.common.utils.wx;

import com.karrecy.common.utils.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 微信小程序订阅消息管理工具类
 * 用于处理微信订阅消息的一次性授权机制
 */
@Slf4j
@Component
public class WxSubscriptionManager {

    private static final String SUBSCRIPTION_KEY_PREFIX = "wx:subscribe:";
    private static final Duration SUBSCRIPTION_EXPIRE_TIME = Duration.ofDays(7); // 订阅记录保存7天
    
    /**
     * 记录用户订阅状态
     * @param openid 用户openid
     * @param templateId 模板ID
     */
    public void recordUserSubscription(String openid, String templateId) {
        if (openid == null || templateId == null) {
            log.warn("记录用户订阅状态失败：参数不完整 openid={}, templateId={}", openid, templateId);
            return;
        }
        
        String key = buildSubscriptionKey(openid, templateId);
        RedisUtils.setCacheObject(key, Boolean.TRUE, SUBSCRIPTION_EXPIRE_TIME);
        log.info("记录用户订阅状态成功：openid={}, templateId={}", openid, templateId);
    }
    
    /**
     * 检查用户是否已订阅
     * @param openid 用户openid
     * @param templateId 模板ID
     * @return 是否已订阅
     */
    public Boolean checkUserSubscription(String openid, String templateId) {
        if (openid == null || templateId == null) {
            return false;
        }
        
        String key = buildSubscriptionKey(openid, templateId);
        Boolean subscribed = RedisUtils.getCacheObject(key);
        return subscribed != null && subscribed;
    }
    
    /**
     * 移除用户订阅状态(消息发送成功后)
     * @param openid 用户openid
     * @param templateId 模板ID
     */
    public void removeUserSubscription(String openid, String templateId) {
        if (openid == null || templateId == null) {
            return;
        }
        
        String key = buildSubscriptionKey(openid, templateId);
        RedisUtils.deleteObject(key);
        log.info("移除用户订阅状态：openid={}, templateId={}", openid, templateId);
    }
    
    /**
     * 构建订阅记录的缓存key
     */
    private String buildSubscriptionKey(String openid, String templateId) {
        return SUBSCRIPTION_KEY_PREFIX + openid + ":" + templateId;
    }
} 