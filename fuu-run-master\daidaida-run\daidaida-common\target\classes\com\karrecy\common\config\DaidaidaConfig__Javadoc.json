{"doc": " 项目相关配置\n", "fields": [{"name": "name", "doc": " 项目名称\n"}, {"name": "version", "doc": " 版本\n"}, {"name": "copyrightYear", "doc": " 版权年份\n"}, {"name": "payCancelTtl", "doc": " 超时未支付取消时长 (分钟)\n"}, {"name": "autoCompleteTtl", "doc": " 超时未完成自动完成时长 (小时)\n"}, {"name": "completionImagesLimit", "doc": " 完成订单凭证上限 （张）\n"}, {"name": "creditUpperLimit", "doc": " 信用分上限（初始）\n"}, {"name": "creditLowerLimit", "doc": " 信用分下限\n"}, {"name": "creditDeduction", "doc": " 信用分每次扣除\n"}, {"name": "max<PERSON><PERSON><PERSON>", "doc": " 用户地址上限\n"}], "enumConstants": [], "methods": [], "constructors": []}