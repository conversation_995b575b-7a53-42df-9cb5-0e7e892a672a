{"doc": " 缓存的key 常量\n", "fields": [{"name": "ONLINE_TOKEN_KEY", "doc": " 在线用户 redis key\n"}, {"name": "CAPTCHA_CODE_KEY", "doc": " 验证码 redis key\n"}, {"name": "SYS_CONFIG_KEY", "doc": " 参数管理 cache key\n"}, {"name": "SYS_DICT_KEY", "doc": " 字典管理 cache key\n"}, {"name": "REPEAT_SUBMIT_KEY", "doc": " 防重提交 redis key\n"}, {"name": "RATE_LIMIT_KEY", "doc": " 限流 redis key\n"}, {"name": "PWD_ERR_CNT_KEY", "doc": " 登录账户密码错误次数 redis key\n"}, {"name": "PHONE_REQUEST_KEY", "doc": " 用户手机号防刷 redis key\n"}], "enumConstants": [], "methods": [], "constructors": []}