{"doc": " redis队列名称\n", "fields": [{"name": "ORDER_PAY_CANCEL", "doc": " 订单超时未支付自动取消 队列\n"}, {"name": "ORDER_PENDING_ACCEPT_CANCEL", "doc": " 订单超时未接单自动取消 队列\n"}, {"name": "ORDER_DELIVERED_AUTO_COMPLETE", "doc": " 订单送达未完成自动完成 队列\n"}, {"name": "ORDER_PAY_SUCCESS", "doc": " 订单付款成功 队列\n"}, {"name": "ORDER_REFUND_SUCCESS", "doc": " 订单退款成功 队列\n"}, {"name": "ORDER_CHAT_STORAGE", "doc": " 订单聊天异步存储 队列\n"}, {"name": "ORDER_CHAT_NOTIFY", "doc": " 订单聊天异步通知 队列\n"}], "enumConstants": [], "methods": [], "constructors": []}