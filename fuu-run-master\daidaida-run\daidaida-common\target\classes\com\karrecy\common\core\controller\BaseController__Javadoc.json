{"doc": " web层通用数据处理\n", "fields": [], "enumConstants": [], "methods": [{"name": "toAjax", "paramTypes": ["int"], "doc": " 响应返回结果\n\n @param rows 影响行数\n @return 操作结果\n"}, {"name": "toAjax", "paramTypes": ["boolean"], "doc": " 响应返回结果\n\n @param result 结果\n @return 操作结果\n"}, {"name": "redirect", "paramTypes": ["java.lang.String"], "doc": " 页面跳转\n"}, {"name": "getLoginUser", "paramTypes": [], "doc": " 获取用户缓存信息\n"}, {"name": "getUserId", "paramTypes": [], "doc": " 获取登录用户id\n"}], "constructors": []}