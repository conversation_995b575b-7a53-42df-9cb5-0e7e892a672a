{"doc": " 每日数据统计表\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "totalOrders", "doc": " 总订单量\n"}, {"name": "canceledOrders", "doc": " 取消订单量\n"}, {"name": "appealedOrders", "doc": " 申诉订单量\n"}, {"name": "completedOrders", "doc": " 完成订单量\n"}, {"name": "completionRate", "doc": " 完单率(%)\n"}, {"name": "deliveryOrders", "doc": " 帮取送订单量\n"}, {"name": "purchaseOrders", "doc": " 代买订单量\n"}, {"name": "universalOrders", "doc": " 万能服务订单量\n"}, {"name": "deliveryRate", "doc": " 帮取送订单占比(%)\n"}, {"name": "purchaseRate", "doc": " 代买订单占比(%)\n"}, {"name": "universalRate", "doc": " 万能订单占比(%)\n"}, {"name": "totalPayment", "doc": " 总收款金额\n"}, {"name": "totalRefund", "doc": " 总退款金额\n"}, {"name": "platformProfit", "doc": " 平台总收益\n"}, {"name": "agentProfit", "doc": " 代理总收益\n"}, {"name": "runnerProfit", "doc": " 跑腿总收益\n"}, {"name": "totalVisits", "doc": " 总访问量\n"}, {"name": "uniqueVisitors", "doc": " 独立访问用户数\n"}, {"name": "maliciousRequests", "doc": " 恶意请求数量\n"}, {"name": "newUsers", "doc": " 新增用户数\n"}, {"name": "activeUsers", "doc": " 活跃用户数\n"}, {"name": "newRunners", "doc": " 新增跑腿用户数\n"}, {"name": "activeRunners", "doc": " 活跃跑腿用户数\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "beginTime", "doc": " 搜索值\n"}, {"name": "endTime", "doc": " 搜索值\n"}], "enumConstants": [], "methods": [], "constructors": []}