{"doc": " 平台管理员表\n", "fields": [{"name": "username", "doc": " 用户名\n"}, {"name": "password", "doc": " 密码\n"}, {"name": "phone", "doc": " 手机号\n"}, {"name": "name", "doc": " 真实姓名\n"}, {"name": "studentCardUrl", "doc": " 学生证\n"}, {"name": "idCardUrl", "doc": " 身份证\n"}, {"name": "sex", "doc": " 0 女 1 男\n"}, {"name": "status", "doc": " 0 禁用 1 启用\n"}, {"name": "avatar", "doc": " 头像\n"}, {"name": "emailEnable", "doc": " 0 禁用 1 启用\n"}, {"name": "email", "doc": " 头像\n"}, {"name": "params", "doc": " 请求参数\n"}], "enumConstants": [], "methods": [], "constructors": []}