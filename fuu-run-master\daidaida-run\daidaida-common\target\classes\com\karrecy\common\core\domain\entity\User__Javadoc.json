{"doc": " 全局用户表\n", "fields": [{"name": "uid", "doc": " 全局uid\n"}, {"name": "deviceType", "doc": " 0 pc 1 小程序\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "loginTime", "doc": " 上次登录时间\n"}, {"name": "loginIp", "doc": " 登录ip\n"}, {"name": "loginRegion", "doc": " 登录地址\n"}, {"name": "userType", "doc": " 用户类型 0 超级管理员 1 校区管理员 2 普通管理员 3 普通用户 4 跑腿用户\n"}, {"name": "createId", "doc": " 创建人\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "updateId", "doc": " 更新人\n"}], "enumConstants": [], "methods": [{"name": "getLoginId", "paramTypes": [], "doc": " 获取登录id\n"}], "constructors": []}