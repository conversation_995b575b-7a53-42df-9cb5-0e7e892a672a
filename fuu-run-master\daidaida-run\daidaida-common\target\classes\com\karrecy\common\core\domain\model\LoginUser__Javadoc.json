{"doc": " 登录用户身份权限\n", "fields": [{"name": "uid", "doc": " 全局id\n"}, {"name": "token", "doc": " 用户唯一标识\n"}, {"name": "userType", "doc": " 用户类型\n"}, {"name": "deviceType", "doc": " 设备类型\n"}, {"name": "loginTime", "doc": " 登录时间\n"}, {"name": "expireTime", "doc": " 过期时间\n"}, {"name": "loginIp", "doc": " 登录IP地址\n"}, {"name": "loginRegion", "doc": " 登录地点\n"}, {"name": "menuPermission", "doc": " 菜单权限\n"}], "enumConstants": [], "methods": [{"name": "getLoginId", "paramTypes": [], "doc": " 获取登录id\n"}], "constructors": []}