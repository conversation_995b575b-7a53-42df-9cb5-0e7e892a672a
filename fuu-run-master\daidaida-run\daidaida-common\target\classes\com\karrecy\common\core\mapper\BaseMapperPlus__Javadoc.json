{"doc": " 自定义 Mapper 接口, 实现 自定义扩展\n", "fields": [], "enumConstants": [], "methods": [{"name": "insertBatch", "paramTypes": ["java.util.Collection"], "doc": " 批量插入\n"}, {"name": "updateBatchById", "paramTypes": ["java.util.Collection"], "doc": " 批量更新\n"}, {"name": "insertOrUpdateBatch", "paramTypes": ["java.util.Collection"], "doc": " 批量插入或更新\n"}, {"name": "insertBatch", "paramTypes": ["java.util.Collection", "int"], "doc": " 批量插入(包含限制条数)\n"}, {"name": "updateBatchById", "paramTypes": ["java.util.Collection", "int"], "doc": " 批量更新(包含限制条数)\n"}, {"name": "insertOrUpdateBatch", "paramTypes": ["java.util.Collection", "int"], "doc": " 批量插入或更新(包含限制条数)\n"}, {"name": "insertOrUpdate", "paramTypes": ["java.lang.Object"], "doc": " 插入或更新(包含限制条数)\n"}, {"name": "selectVoById", "paramTypes": ["java.io.Serializable", "java.lang.Class"], "doc": " 根据 ID 查询\n"}, {"name": "selectVoBatchIds", "paramTypes": ["java.util.Collection", "java.lang.Class"], "doc": " 查询（根据ID 批量查询）\n"}, {"name": "selectVoByMap", "paramTypes": ["java.util.Map", "java.lang.Class"], "doc": " 查询（根据 columnMap 条件）\n"}, {"name": "selectVoOne", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class"], "doc": " 根据 entity 条件，查询一条记录\n"}, {"name": "selectVoList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class"], "doc": " 根据 entity 条件，查询全部记录\n"}, {"name": "selectVoPage", "paramTypes": ["com.baomidou.mybatisplus.core.metadata.IPage", "com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class"], "doc": " 分页查询VO\n"}], "constructors": []}