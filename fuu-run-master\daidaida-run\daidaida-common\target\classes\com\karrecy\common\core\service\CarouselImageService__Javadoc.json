{"doc": " 轮播图管理\n", "fields": [], "enumConstants": [], "methods": [{"name": "addCarouselImage", "paramTypes": ["java.lang.String"], "doc": " 添加一张新的轮播图图片（不限制 base64 字符串长度）\n @param base64Content 不带 data URL 前缀的 Base64 编码字符串\n"}, {"name": "deleteCarouselImage", "paramTypes": ["int"], "doc": " 删除指定索引处的轮播图图片\n @param index 要删除的图片索引（从 0 开始）\n @throws IllegalArgumentException 如果索引不合法\n"}, {"name": "updateCarouselImage", "paramTypes": ["int", "java.lang.String"], "doc": " 修改指定索引处的轮播图图片\n @param index 要修改的图片索引，从0开始\n @param base64Content 不带 data URL 前缀的 Base64 编码字符串（例如 \"xxxx\"）\n"}], "constructors": []}