{"doc": " 登录鉴权助手\n", "fields": [], "enumConstants": [], "methods": [{"name": "loginByDevice", "paramTypes": ["com.karrecy.common.core.domain.model.LoginUser"], "doc": " 登录系统\n @param loginUser 登录用户信息\n"}, {"name": "getLoginUser", "paramTypes": [], "doc": " 获取用户(多级缓存)\n"}, {"name": "getLoginUser", "paramTypes": ["java.lang.String"], "doc": " 获取用户基于token\n"}, {"name": "getUserType", "paramTypes": [], "doc": " 获取用户类型\n"}, {"name": "getUserId", "paramTypes": [], "doc": " 获取用户id\n"}, {"name": "isAdmin", "paramTypes": ["java.lang.Long"], "doc": " 是否为管理员\n\n @param userId 用户ID\n @return 结果\n"}], "constructors": []}