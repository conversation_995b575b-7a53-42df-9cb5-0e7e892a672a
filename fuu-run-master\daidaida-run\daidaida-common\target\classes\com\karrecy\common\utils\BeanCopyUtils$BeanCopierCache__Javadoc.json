{"doc": " BeanCopier属性缓存<br>\n 缓存用于防止多次反射造成的性能问题\n\n <AUTHOR>\n @since 5.4.1\n", "fields": [], "enumConstants": [{"name": "INSTANCE", "doc": " BeanCopier属性缓存单例\n"}], "methods": [{"name": "get", "paramTypes": ["java.lang.Class", "java.lang.Class", "org.springframework.cglib.core.Converter"], "doc": " 获得类与转换器生成的key在{@link BeanCopier}的Map中对应的元素\n\n @param srcClass    源Bean的类\n @param targetClass 目标Bean的类\n @param converter   转换器\n @return Map中对应的BeanCopier\n"}, {"name": "gen<PERSON><PERSON>", "paramTypes": ["java.lang.Class", "java.lang.Class", "org.springframework.cglib.core.Converter"], "doc": " 获得类与转换器生成的key\n\n @param srcClass    源Bean的类\n @param targetClass 目标Bean的类\n @param converter   转换器\n @return 属性名和Map映射的key\n"}], "constructors": []}