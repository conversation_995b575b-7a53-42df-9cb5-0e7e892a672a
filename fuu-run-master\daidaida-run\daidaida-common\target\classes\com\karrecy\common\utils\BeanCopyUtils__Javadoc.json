{"doc": " bean拷贝工具\n", "fields": [], "enumConstants": [], "methods": [{"name": "copy", "paramTypes": ["java.lang.Object", "java.lang.Class"], "doc": " 单对象基于class创建拷贝\n\n @param source 数据来源实体\n @param desc   描述对象 转换后的对象\n @return desc\n"}, {"name": "copy", "paramTypes": ["java.lang.Object", "java.lang.Object"], "doc": " 单对象基于对象创建拷贝\n\n @param source 数据来源实体\n @param desc   转换后的对象\n @return desc\n"}, {"name": "copyList", "paramTypes": ["java.util.List", "java.lang.Class"], "doc": " 列表对象基于class创建拷贝\n\n @param sourceList 数据来源实体列表\n @param desc       描述对象 转换后的对象\n @return desc\n"}, {"name": "copyToMap", "paramTypes": ["java.lang.Object"], "doc": " bean拷贝到map\n\n @param bean 数据来源实体\n @return map对象\n"}, {"name": "mapToBean", "paramTypes": ["java.util.Map", "java.lang.Class"], "doc": " map拷贝到bean\n\n @param map       数据来源\n @param beanClass bean类\n @return bean对象\n"}, {"name": "mapToBean", "paramTypes": ["java.util.Map", "java.lang.Object"], "doc": " map拷贝到bean\n\n @param map  数据来源\n @param bean bean对象\n @return bean对象\n"}, {"name": "mapToMap", "paramTypes": ["java.util.Map", "java.lang.Class"], "doc": " map拷贝到map\n\n @param map   数据来源\n @param clazz 返回的对象类型\n @return map对象\n"}], "constructors": []}