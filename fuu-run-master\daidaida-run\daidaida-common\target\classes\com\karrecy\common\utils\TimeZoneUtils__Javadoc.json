{"doc": "\n 时区工具类\r\n 用于处理时间相关的问题，确保时间的准确性\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getNow", "paramTypes": [], "doc": "\n 获取当前时间（系统默认时区）\r\n"}, {"name": "getNowInChina", "paramTypes": [], "doc": "\n 获取当前时间（中国标准时区）\r\n"}, {"name": "convertToZone", "paramTypes": ["java.time.LocalDateTime", "java.time.ZoneId"], "doc": "\n 将LocalDateTime转换为指定时区\r\n"}, {"name": "convertToChina", "paramTypes": ["java.time.LocalDateTime"], "doc": "\n 将LocalDateTime转换为中国标准时区\r\n"}, {"name": "formatDateTime", "paramTypes": ["java.time.LocalDateTime"], "doc": "\n 格式化LocalDateTime为标准字符串\r\n"}, {"name": "getNowString", "paramTypes": [], "doc": "\n 获取当前时间的格式化字符串（中国标准时区）\r\n"}], "constructors": []}