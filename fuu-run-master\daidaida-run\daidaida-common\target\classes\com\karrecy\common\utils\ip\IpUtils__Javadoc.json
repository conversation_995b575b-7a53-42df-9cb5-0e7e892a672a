{"doc": " 获取IP方法\n", "fields": [], "enumConstants": [], "methods": [{"name": "getIpAddr", "paramTypes": [], "doc": " 获取客户端IP\n\n @return IP地址\n"}, {"name": "getIpAddr", "paramTypes": ["javax.servlet.http.HttpServletRequest"], "doc": " 获取客户端IP\n\n @param request 请求对象\n @return IP地址\n"}, {"name": "internalIp", "paramTypes": ["java.lang.String"], "doc": " 检查是否为内部IP地址\n\n @param ip IP地址\n @return 结果\n"}, {"name": "internalIp", "paramTypes": ["byte[]"], "doc": " 检查是否为内部IP地址\n\n @param addr byte地址\n @return 结果\n"}, {"name": "textToNumericFormatV4", "paramTypes": ["java.lang.String"], "doc": " 将IPv4地址转换成字节\n\n @param text IPv4地址\n @return byte 字节\n"}, {"name": "getHostIp", "paramTypes": [], "doc": " 获取IP地址\n\n @return 本地IP地址\n"}, {"name": "getHostName", "paramTypes": [], "doc": " 获取主机名\n\n @return 本地主机名\n"}, {"name": "getMultistageReverseProxyIp", "paramTypes": ["java.lang.String"], "doc": " 从多级反向代理中获得第一个非unknown IP地址\n\n @param ip 获得的IP地址\n @return 第一个非unknown IP地址\n"}, {"name": "isUnknown", "paramTypes": ["java.lang.String"], "doc": " 检测给定字符串是否为未知，多用于检测HTTP请求相关\n\n @param checkString 被检测的字符串\n @return 是否未知\n"}, {"name": "isIP", "paramTypes": ["java.lang.String"], "doc": " 是否为IP\n"}, {"name": "isIpWildCard", "paramTypes": ["java.lang.String"], "doc": " 是否为IP，或 *为间隔的通配符地址\n"}, {"name": "ipIsInWildCardNoCheck", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 检测参数是否在ip通配符里\n"}, {"name": "isIPSegment", "paramTypes": ["java.lang.String"], "doc": " 是否为特定格式如:“**********-***********”的ip段字符串\n"}, {"name": "ipIsInNetNoCheck", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 判断ip是否在指定网段中\n"}, {"name": "isMatchedIp", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 校验ip是否符合过滤串规则\n\n @param filter 过滤IP列表,支持后缀'*'通配,支持网段如:`**********-***********`\n @param ip 校验IP地址\n @return boolean 结果\n"}], "constructors": []}