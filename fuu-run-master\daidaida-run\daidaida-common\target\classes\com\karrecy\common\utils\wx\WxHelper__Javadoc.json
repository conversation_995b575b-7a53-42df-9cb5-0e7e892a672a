{"doc": " 小程序订阅消息类\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendSubMsg", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发送订阅消息\n 注意：仅在用户已授权的情况下才会发送，授权是一次性的\n"}, {"name": "recordSubscription", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 记录用户订阅授权状态\n 前端调用wx.requestSubscribeMessage成功后应调用此方法\n"}, {"name": "buildOrderStatusData", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 构建订单状态消息数据\n @param orderId\n @param title\n @param status\n @param remarks\n @return\n"}, {"name": "buildPriceAdjustmentData", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 构建补差价通知消息数据\n @param orderId 订单号\n @param orderType 订单类型\n @param amount 金额\n @return 消息数据列表\n"}, {"name": "buildMsgUnReadData", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 构建未读消息数据\n @param orderId\n @param content\n @param senderName\n @param remarks\n @return\n"}], "constructors": []}