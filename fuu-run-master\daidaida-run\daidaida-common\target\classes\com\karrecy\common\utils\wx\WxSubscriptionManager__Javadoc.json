{"doc": "\n 微信小程序订阅消息管理工具类\r\n 用于处理微信订阅消息的一次性授权机制\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "recordUserSubscription", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 记录用户订阅状态\r\n @param openid 用户openid\r\n @param templateId 模板ID\r\n"}, {"name": "checkUserSubscription", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 检查用户是否已订阅\r\n @param openid 用户openid\r\n @param templateId 模板ID\r\n @return 是否已订阅\r\n"}, {"name": "removeUserSubscription", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 移除用户订阅状态(消息发送成功后)\r\n @param openid 用户openid\r\n @param templateId 模板ID\r\n"}, {"name": "buildSubscriptionKey", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 构建订阅记录的缓存key\r\n"}], "constructors": []}