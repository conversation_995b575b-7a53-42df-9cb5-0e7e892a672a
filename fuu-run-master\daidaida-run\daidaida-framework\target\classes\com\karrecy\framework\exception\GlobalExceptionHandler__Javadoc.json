{"doc": " 全局异常处理器\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleOrderException", "paramTypes": ["com.karrecy.common.exception.OrderException", "javax.servlet.http.HttpServletRequest"], "doc": " 业务异常\n"}, {"name": "handleServiceException", "paramTypes": ["com.karrecy.common.exception.ServiceException", "javax.servlet.http.HttpServletRequest"], "doc": " 业务异常\n"}, {"name": "handleNotPermissionException", "paramTypes": ["cn.dev33.satoken.exception.NotPermissionException", "javax.servlet.http.HttpServletRequest"], "doc": " 权限码异常\n"}, {"name": "handleNotRoleException", "paramTypes": ["cn.dev33.satoken.exception.NotRoleException", "javax.servlet.http.HttpServletRequest"], "doc": " 角色权限异常\n"}, {"name": "handleNotLoginException", "paramTypes": ["cn.dev33.satoken.exception.NotLoginException", "javax.servlet.http.HttpServletRequest"], "doc": " 认证失败\n"}, {"name": "handleHttpRequestMethodNotSupported", "paramTypes": ["org.springframework.web.HttpRequestMethodNotSupportedException", "javax.servlet.http.HttpServletRequest"], "doc": " 请求方式不支持\n"}, {"name": "handleDuplicateKeyException", "paramTypes": ["org.springframework.dao.DuplicateKeyException", "javax.servlet.http.HttpServletRequest"], "doc": " 主键或UNIQUE索引，数据重复异常\n"}, {"name": "handleDataIntegrityViolationException", "paramTypes": ["org.springframework.dao.DataIntegrityViolationException", "javax.servlet.http.HttpServletRequest"], "doc": " 数据完整性违规异常处理 /基本用不到，防止忘记加validation注解\n"}, {"name": "handleCannotFindDataSourceException", "paramTypes": ["org.mybatis.spring.MyBatisSystemException", "javax.servlet.http.HttpServletRequest"], "doc": " Mybat<PERSON>系统异常 通用处理\n"}, {"name": "handleBaseException", "paramTypes": ["com.karrecy.common.exception.base.BaseException", "javax.servlet.http.HttpServletRequest"], "doc": " 业务异常\n"}, {"name": "handleMissingPathVariableException", "paramTypes": ["org.springframework.web.bind.MissingPathVariableException", "javax.servlet.http.HttpServletRequest"], "doc": " 请求路径中缺少必需的路径变量\n"}, {"name": "handleMethodArgumentTypeMismatchException", "paramTypes": ["org.springframework.web.method.annotation.MethodArgumentTypeMismatchException", "javax.servlet.http.HttpServletRequest"], "doc": " 请求参数类型不匹配\n"}, {"name": "handleRuntimeException", "paramTypes": ["java.lang.RuntimeException", "javax.servlet.http.HttpServletRequest"], "doc": " 拦截未知的运行时异常\n"}, {"name": "handleBindException", "paramTypes": ["org.springframework.validation.BindException"], "doc": " 自定义验证异常\n"}, {"name": "handleException", "paramTypes": ["java.lang.Exception", "javax.servlet.http.HttpServletRequest"], "doc": " 系统异常\n"}, {"name": "handleMethodArgumentNotValidException", "paramTypes": ["org.springframework.web.bind.MethodArgumentNotValidException"], "doc": " 自定义验证异常\n"}], "constructors": []}