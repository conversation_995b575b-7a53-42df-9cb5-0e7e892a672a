{"doc": " 服务器相关信息\n", "fields": [{"name": "cpu", "doc": " CPU相关信息\n"}, {"name": "mem", "doc": " 內存相关信息\n"}, {"name": "jvm", "doc": " JVM相关信息\n"}, {"name": "sys", "doc": " 服务器相关信息\n"}, {"name": "sysFiles", "doc": " 磁盘相关信息\n"}], "enumConstants": [], "methods": [{"name": "setCpuInfo", "paramTypes": ["oshi.hardware.CentralProcessor"], "doc": " 设置CPU信息\n"}, {"name": "setMemInfo", "paramTypes": ["oshi.hardware.GlobalMemory"], "doc": " 设置内存信息\n"}, {"name": "setSysInfo", "paramTypes": [], "doc": " 设置服务器信息\n"}, {"name": "setJvmInfo", "paramTypes": [], "doc": " 设置Java虚拟机\n"}, {"name": "initSysFiles", "paramTypes": ["oshi.software.os.OperatingSystem"], "doc": " 设置磁盘信息\n"}, {"name": "convertFileSize", "paramTypes": ["long"], "doc": " 字节转换\n\n @param size 字节大小\n @return 转换后值\n"}], "constructors": []}