{"doc": " JVM相关信息\n", "fields": [{"name": "total", "doc": " 当前JVM占用的内存总数(M)\n"}, {"name": "max", "doc": " JVM最大可用内存总数(M)\n"}, {"name": "free", "doc": " JVM空闲内存(M)\n"}, {"name": "version", "doc": " JDK版本\n"}, {"name": "home", "doc": " JDK路径\n"}], "enumConstants": [], "methods": [{"name": "getName", "paramTypes": [], "doc": " 获取JDK名称\n"}, {"name": "getStartTime", "paramTypes": [], "doc": " JDK启动时间\n"}, {"name": "getRunTime", "paramTypes": [], "doc": " JDK运行时间\n"}, {"name": "getInputArgs", "paramTypes": [], "doc": " 运行参数\n"}], "constructors": []}