{"doc": " netty连接池\n", "fields": [], "enumConstants": [], "methods": [{"name": "addChannel", "paramTypes": ["java.lang.Long", "io.netty.channel.Channel"], "doc": " 新增一个客户端通道\n如果觉得channl的id的asLongText()方法过于冗长可以推荐使用业务:id:id的形式作为key\n @param channel 新的通道\n"}, {"name": "removeChannel", "paramTypes": ["io.netty.channel.Channel"], "doc": " 移除一个客户端连接通道\n\n @param channel 要移除的通道\n"}, {"name": "getChannels", "paramTypes": [], "doc": " 获取所有活跃的客户端连接\n\n @return 活跃通道组\n"}, {"name": "getChannelById", "paramTypes": ["java.lang.Long"], "doc": " 通过通道 ID 获取通道\n\n @param uid 通道 ID\n @return 通道，若未找到则返回 null\n"}, {"name": "broadcastMessage", "paramTypes": ["java.lang.String"], "doc": " 向所有连接的客户端广播消息\n\n @param message 要广播的消息\n"}], "constructors": []}