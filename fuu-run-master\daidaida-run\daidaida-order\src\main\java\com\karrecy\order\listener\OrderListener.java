package com.karrecy.order.listener;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.karrecy.common.constant.QueueNames;
import com.karrecy.common.constant.UserConstants;
import com.karrecy.common.core.domain.R;
import com.karrecy.common.core.domain.entity.User;
import com.karrecy.common.core.domain.entity.UserWx;
import com.karrecy.common.core.domain.model.LoginUser;
import com.karrecy.common.enums.OrderStatus;
import com.karrecy.common.enums.PayStatus;
import com.karrecy.common.enums.UserType;
import com.karrecy.common.helper.LoginHelper;
import com.karrecy.common.utils.redis.QueueUtils;
import com.karrecy.common.utils.wx.WxHelper;
import com.karrecy.order.domain.po.OrderAppeal;
import com.karrecy.order.domain.po.OrderMain;
import com.karrecy.order.domain.po.OrderPayment;
import com.karrecy.order.domain.po.OrderProgress;
import com.karrecy.order.domain.po.School;
import com.karrecy.system.domain.po.Wallet;
import com.karrecy.payment.domain.po.CapitalFlow;
import com.karrecy.common.constant.UserConstants;
import com.karrecy.common.enums.CapitalType;
import com.karrecy.common.utils.BigDecimalUtils;
import com.karrecy.order.mapper.OrderAppealMapper;
import com.karrecy.order.service.IOrderChatService;
import com.karrecy.order.service.IOrderMainService;
import com.karrecy.order.service.IOrderPaymentService;
import com.karrecy.order.service.IOrderProgressService;
import com.karrecy.payment.service.IPayService;
import com.karrecy.system.mapper.UserWxMapper;
import com.karrecy.order.mapper.SchoolMapper;
import com.karrecy.order.mapper.OrderPaymentMapper;
import com.karrecy.system.mapper.WalletMapper;
import com.karrecy.payment.mapper.CapitalFlowMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;


/**
 * @订单操作监听 解耦
 */
@Component
@Slf4j
@AllArgsConstructor
public class OrderListener implements ApplicationListener<ApplicationReadyEvent> {

    private IOrderMainService orderMainService;
    private IOrderProgressService orderProgressService;
    private IOrderPaymentService orderPaymentService;
    private IPayService payService;
    private IOrderChatService orderChatService;

    private final UserWxMapper userWxMapper;
    private final WxHelper wxHelper;
    private final SchoolMapper schoolMapper;
    private final OrderPaymentMapper orderPaymentMapper;
    private final WalletMapper walletMapper;
    private final CapitalFlowMapper capitalFlowMapper;
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        subscribe(QueueNames.ORDER_PAY_CANCEL);
        subscribe(QueueNames.ORDER_PENDING_ACCEPT_CANCEL);
        subscribe(QueueNames.ORDER_DELIVERED_AUTO_COMPLETE);

    }
    public R<Void> subscribe(String queueName) {
        log.info("通道: {} 监听中......", queueName);
        // 项目初始化设置一次即可
        // 对于延迟队列，需要使用isDelayed=true参数
        boolean isDelayed = QueueNames.ORDER_DELIVERED_AUTO_COMPLETE.equals(queueName);

        QueueUtils.subscribeBlockingQueue(queueName, (String data) -> {
            // 观察接收时间
            log.info("通道: {}, 收到数据: {}", queueName, data);
            switch (queueName) {
                case QueueNames.ORDER_PAY_CANCEL:
                    payCancel(data);
                    break;
                case QueueNames.ORDER_PENDING_ACCEPT_CANCEL:
                    pendingAcceptCancel(data);
                    break;
                case QueueNames.ORDER_DELIVERED_AUTO_COMPLETE:
                    deliveredAutoComplete(data);
                    break;
            }


        }, isDelayed);
        return R.ok("操作成功");
    }

    public void payCancel(String orderNum) {
        log.info("处理订单超时未付款 自动取消");
        try {
            OrderMain orderMainDB = orderMainService.getById(orderNum);
            if (!ObjectUtil.equals(orderMainDB.getStatus(), OrderStatus.PENDING_PAYMENT.getCode())) {
                return;
            }
            //设置状态为取消
            boolean update = orderMainService.update(
                    new LambdaUpdateWrapper<OrderMain>()
                            .set(OrderMain::getStatus, OrderStatus.CANCELED.getCode())
                            .eq(OrderMain::getId, orderMainDB.getId())
                            .eq(OrderMain::getStatus, OrderStatus.PENDING_PAYMENT.getCode())
            );
            if (!update) return;

            OrderProgress orderProgressDB = orderProgressService.getById(orderNum);
            //取消原因
            orderProgressDB.setCancelUserType(UserType.SYSTEM.getCode());
            orderProgressDB.setCancelTime(LocalDateTime.now());
            orderProgressDB.setCancelReason("超时未付款自动取消");
            orderProgressService.updateById(orderProgressDB);
        }
        catch (Exception e) {
            log.error("处理订单超时未付款 出错，{}", e.getMessage());
        }
    }

    public void pendingAcceptCancel(String orderNum) {
        log.info("处理订单超时未接单 自动取消");
        try {
            OrderMain orderMainDB = orderMainService.getById(orderNum);
            if (!ObjectUtil.equals(orderMainDB.getStatus(), OrderStatus.PENDING_ACCEPTANCE.getCode())) {
                return;
            }
            //设置状态为取消
            boolean update = orderMainService.update(
                    new LambdaUpdateWrapper<OrderMain>()
                            .set(OrderMain::getStatus, OrderStatus.CANCELED.getCode())
                            .eq(OrderMain::getId, orderMainDB.getId())
                            .eq(OrderMain::getStatus, OrderStatus.PENDING_ACCEPTANCE.getCode())
            );
            if (!update) return;

            OrderProgress orderProgressDB = orderProgressService.getById(orderNum);
            //取消原因
            orderProgressDB.setCancelUserType(UserType.SYSTEM.getCode());
            orderProgressDB.setCancelTime(LocalDateTime.now());
            orderProgressDB.setCancelReason("超时未接单自动取消");
            orderProgressService.updateById(orderProgressDB);
            //退款
            OrderPayment orderPaymentDB = orderPaymentService.getById(orderNum);
            orderPaymentDB.setRefundPendingTime(LocalDateTime.now());
            orderPaymentDB.setPaymentStatus(PayStatus.REFUND_IN_PROGRESS.getCode());
            orderPaymentService.updateById(orderPaymentDB);
            //退款 全额退款
            payService.refund(Long.valueOf(orderNum), orderPaymentDB.getActualPayment(), orderPaymentDB.getActualPayment());
            //通知用户
            List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildOrderStatusData(
                    Long.valueOf(orderNum),
                    orderMainDB.getTag(),
                    OrderStatus.getString(OrderStatus.CANCELED.getCode()),
                    "超时未接单，已取消"
            );
            UserWx orderUser = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>().eq(UserWx::getUid, orderMainDB.getUserId()));
            wxHelper.sendSubMsg(
                    msgData,
                    WxHelper.PAGE_ORDER_DETAIL+"?id="+orderNum,
                    WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                    orderUser.getOpenid()
            );

        }
        catch (Exception e) {
            log.error("处理订单超时未接单 出错，{}", e.getMessage());
        }

    }

    public void deliveredAutoComplete(String orderNum) {
        log.info("处理订单送达未完成 自动完成");
        try {
            Long orderId = Long.valueOf(orderNum);
            OrderMain orderMainDB = orderMainService.getById(orderId);
            if (!ObjectUtil.equals(orderMainDB.getStatus(), OrderStatus.DELIVERED.getCode())) {
                log.info("订单状态已变更，不需自动完成，订单ID: {}, 当前状态: {}", orderId, orderMainDB.getStatus());
                return;
            }

            log.info("开始系统自动确认订单完成，订单ID: {}", orderId);

            // 系统自动确认，绕过权限检查
            autoConfirmOrderBySystem(orderId, orderMainDB);
            log.info("系统自动确认订单完成成功，订单ID: {}", orderId);

        }
        catch (Exception e) {
            log.error("处理订单送达未完成 自动完成出错，订单号: {}, 错误: {}", orderNum, e.getMessage(), e);
        }
    }

    /**
     * 系统自动确认订单完成（绕过权限检查）
     */
    private void autoConfirmOrderBySystem(Long orderId, OrderMain orderMain) {
        try {
            // 更新订单状态为已完成
            orderMain.setStatus(OrderStatus.COMPLETED.getCode());
            orderMainService.updateById(orderMain);

            // 更新订单进度
            OrderProgress orderProgress = orderProgressService.getById(orderId);
            if (orderProgress != null) {
                orderProgress.setCompletedTime(LocalDateTime.now());
                orderProgress.setCompletedType(UserType.SYSTEM.getCode()); // 系统自动确认
                orderProgressService.updateById(orderProgress);
            }

            log.info("系统自动确认订单完成，订单ID: {}, 状态已更新为: {}", orderId, OrderStatus.COMPLETED.getCode());

            // 执行收益分发逻辑
            performProfitDistribution(orderId, orderMain);

            log.info("系统自动确认订单完成，包含收益分发，订单ID: {}", orderId);

            // 异步发送微信通知（避免在异步监听器中调用同步Redis操作）
            try {
                UserWx orderUser = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>().eq(UserWx::getUid, orderMain.getUserId()));
                if (orderUser != null) {
                    // 使用异步方式发送通知，避免阻塞
                    CompletableFuture.runAsync(() -> {
                        try {
                            List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildOrderStatusData(
                                    orderId,
                                    orderMain.getTag(),
                                    OrderStatus.getString(OrderStatus.COMPLETED.getCode()),
                                    "订单已自动确认完成"
                            );
                            wxHelper.sendSubMsg(
                                    msgData,
                                    WxHelper.PAGE_ORDER_DETAIL+"?id="+orderId,
                                    WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                                    orderUser.getOpenid()
                            );
                            log.info("已发送自动确认完成通知，订单ID: {}, 用户ID: {}", orderId, orderMain.getUserId());
                        } catch (Exception e) {
                            log.warn("发送微信通知失败，订单ID: {}, 错误: {}", orderId, e.getMessage());
                        }
                    });
                }
            } catch (Exception e) {
                log.warn("准备发送微信通知时出错，订单ID: {}, 错误: {}", orderId, e.getMessage());
            }

        } catch (Exception e) {
            log.error("系统自动确认订单失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行收益分发逻辑
     */
    private void performProfitDistribution(Long orderId, OrderMain orderMain) {
        try {
            log.info("开始执行收益分发，订单ID: {}", orderId);

            // 获取学校信息
            School schoolDB = schoolMapper.selectById(orderMain.getSchoolId());
            if (schoolDB == null) {
                log.error("学校信息不存在，订单ID: {}, 学校ID: {}", orderId, orderMain.getSchoolId());
                return;
            }

            // 计算收益占比
            Integer profitPlat = schoolDB.getProfitPlat() + schoolDB.getProfitAgent(); // 平台收益占比(合并代理收益)
            Integer profitAgent = 0; // 取消代理收益
            Integer profitRunner = schoolDB.getProfitRunner(); // 跑腿收益占比
            Integer totalProfit = profitRunner + profitPlat;

            if (totalProfit <= 0) {
                log.error("收益占比配置错误，订单ID: {}, 总占比: {}", orderId, totalProfit);
                return;
            }

            // 获取订单支付信息
            OrderPayment orderPaymentDB = orderPaymentMapper.selectById(orderId);
            if (orderPaymentDB == null) {
                log.error("订单支付信息不存在，订单ID: {}", orderId);
                return;
            }

            BigDecimal totalAmount = orderMain.getTotalAmount();
            log.info("订单收益分发详情，订单ID: {}, 总金额: {}, 平台占比: {}%, 跑腿员占比: {}%",
                    orderId, totalAmount, profitPlat, profitRunner);

            // 计算各方收益
            BigDecimal profitPlatAmount = BigDecimalUtils.multiply(totalAmount,
                    BigDecimalUtils.divide(new BigDecimal(profitPlat), new BigDecimal(totalProfit)));
            BigDecimal profitAgentAmount = BigDecimal.ZERO; // 代理收益设为零
            BigDecimal profitRunnerAmount = BigDecimalUtils.multiply(totalAmount,
                    BigDecimalUtils.divide(new BigDecimal(profitRunner), new BigDecimal(totalProfit)));

            log.info("收益计算结果，订单ID: {}, 平台收益: {}, 跑腿员收益: {}",
                    orderId, profitPlatAmount, profitRunnerAmount);

            // 更新钱包余额
            updateWalletBalances(orderId, orderMain.getRunnerId(), profitPlatAmount, profitRunnerAmount);

            // 插入资金流动记录
            insertCapitalFlowRecord(orderId, orderMain, orderPaymentDB, profitPlatAmount, profitAgentAmount, profitRunnerAmount);

            log.info("收益分发完成，订单ID: {}", orderId);

        } catch (Exception e) {
            log.error("收益分发失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新钱包余额
     */
    private void updateWalletBalances(Long orderId, Long runnerId, BigDecimal profitPlatAmount, BigDecimal profitRunnerAmount) {
        try {
            // 更新平台钱包
            Long adminId = UserConstants.ADMIN_ID;
            Wallet walletAdmin = walletMapper.selectById(adminId);
            if (walletAdmin != null) {
                walletAdmin.setBalance(BigDecimalUtils.add(walletAdmin.getBalance(), profitPlatAmount));
                walletAdmin.setUpdateTime(LocalDateTime.now());
                walletMapper.updateById(walletAdmin);
                log.info("平台钱包更新成功，订单ID: {}, 增加金额: {}, 新余额: {}",
                        orderId, profitPlatAmount, walletAdmin.getBalance());
            } else {
                log.error("平台钱包不存在，订单ID: {}, 平台ID: {}", orderId, adminId);
            }

            // 更新跑腿员钱包
            Wallet walletRunner = walletMapper.selectById(runnerId);
            if (walletRunner != null) {
                walletRunner.setBalance(BigDecimalUtils.add(walletRunner.getBalance(), profitRunnerAmount));
                walletRunner.setUpdateTime(LocalDateTime.now());
                walletMapper.updateById(walletRunner);
                log.info("跑腿员钱包更新成功，订单ID: {}, 跑腿员ID: {}, 增加金额: {}, 新余额: {}",
                        orderId, runnerId, profitRunnerAmount, walletRunner.getBalance());
            } else {
                log.error("跑腿员钱包不存在，订单ID: {}, 跑腿员ID: {}", orderId, runnerId);
            }

        } catch (Exception e) {
            log.error("更新钱包余额失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 插入资金流动记录
     */
    private void insertCapitalFlowRecord(Long orderId, OrderMain orderMain, OrderPayment orderPayment,
                                       BigDecimal profitPlatAmount, BigDecimal profitAgentAmount, BigDecimal profitRunnerAmount) {
        try {
            CapitalFlow capitalFlow = new CapitalFlow();
            capitalFlow.setOrderId(orderId);
            capitalFlow.setUserId(orderMain.getUserId());
            capitalFlow.setRunnerId(orderMain.getRunnerId());
            capitalFlow.setProfitPlat(profitPlatAmount);
            capitalFlow.setProfitAgent(profitAgentAmount);
            capitalFlow.setProfitRunner(profitRunnerAmount);
            capitalFlow.setProfitUser(BigDecimalUtils.multiply(orderPayment.getActualPayment(), new BigDecimal(-1)));
            capitalFlow.setType(CapitalType.ORDER_COMPLETE.getCode());
            capitalFlow.setCreateTime(LocalDateTime.now());

            capitalFlowMapper.insert(capitalFlow);

            log.info("资金流动记录插入成功，订单ID: {}, 用户支出: {}, 平台收益: {}, 跑腿员收益: {}",
                    orderId, orderPayment.getActualPayment(), profitPlatAmount, profitRunnerAmount);

        } catch (Exception e) {
            log.error("插入资金流动记录失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw e;
        }
    }

}
