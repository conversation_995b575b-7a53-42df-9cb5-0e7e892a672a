package com.karrecy.order.listener;

import com.karrecy.common.constant.CacheNames;
import com.karrecy.common.constant.QueueNames;
import com.karrecy.common.core.domain.R;
import com.karrecy.common.core.domain.entity.User;
import com.karrecy.common.enums.CapitalType;
import com.karrecy.common.enums.OrderStatus;
import com.karrecy.common.enums.PayStatus;
import com.karrecy.common.enums.Status;
import com.karrecy.common.enums.UserType;
import com.karrecy.common.utils.TimeZoneUtils;
import com.karrecy.common.utils.redis.QueueUtils;
import com.karrecy.common.utils.redis.RedisUtils;
import com.karrecy.common.utils.wx.WxHelper;
import com.karrecy.order.domain.po.OrderAppeal;
import com.karrecy.order.domain.po.OrderMain;
import com.karrecy.order.domain.po.OrderPayment;
import com.karrecy.order.domain.po.OrderProgress;
import com.karrecy.order.domain.po.School;
import com.karrecy.order.mapper.OrderAppealMapper;
import com.karrecy.order.mapper.SchoolMapper;
import com.karrecy.order.service.IOrderChatService;
import com.karrecy.order.service.IOrderMainService;
import com.karrecy.order.service.IOrderPaymentService;
import com.karrecy.order.service.IOrderProgressService;
import com.karrecy.system.domain.po.Wallet;
import com.karrecy.system.mapper.WalletMapper;
import com.karrecy.system.service.IUserService;
import com.karrecy.payment.domain.po.CapitalFlow;
import com.karrecy.payment.mapper.CapitalFlowMapper;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;


/**
 * 支付结果监听
 */
@Component
@Slf4j
@AllArgsConstructor
public class PayListener implements ApplicationListener<ApplicationReadyEvent> {

    private IOrderMainService orderMainService;
    private IOrderPaymentService orderPaymentService;
    private IUserService userService;
    private WxHelper wxHelper;
    private OrderAppealMapper orderAppealMapper;
    private WalletMapper walletMapper;
    private SchoolMapper schoolMapper;
    private CapitalFlowMapper capitalFlowMapper;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        subscribe(QueueNames.ORDER_PAY_SUCCESS);
        subscribe(QueueNames.ORDER_REFUND_SUCCESS);

    }
    public R<Void> subscribe(String queueName) {
        log.info("通道: {} 监听中......", queueName);
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(queueName, (String data) -> {
            // 观察接收时间
            log.info("通道: {}, 收到数据: {}", queueName, data);
            // 使用异步方式处理回调
            CompletableFuture.runAsync(() -> {
                try {
                    switch (queueName) {
                        case QueueNames.ORDER_PAY_SUCCESS:
                            paySuccess(data);
                            break;
                        case QueueNames.ORDER_REFUND_SUCCESS:
                            refundSuccess(data);
                            break;
                    }
                } catch (Exception e) {
                    log.error("处理队列消息失败(支付回调）: {}", e.getMessage(), e);
                }
            });
//            switch (queueName) {
//                case QueueNames.ORDER_PAY_SUCCESS:
//                    paySuccess(data);
//                    break;
//                case QueueNames.ORDER_REFUND_SUCCESS:
//                    refundSuccess(data);
//                    break;
//            }


        }, true);
        return R.ok("操作成功");
    }
    @Transactional
    public void paySuccess(String orderNum) {
        try {
            log.info("开始处理支付成功消息，订单号: {}", orderNum);
            if (orderNum == null || orderNum.trim().isEmpty()) {
                log.error("支付成功处理失败：订单号为空");
                return;
            }
            
            // 检查是否是补差价支付（从Redis缓存中查找）
            String cacheKey = "extra_payment:" + orderNum;
            String originalOrderId = RedisUtils.getCacheObject(cacheKey);
            boolean isExtraPayment = originalOrderId != null;
            
            if (isExtraPayment) {
                // 处理补差价支付成功逻辑
                log.info("骑手补差价支付成功，原订单ID为：{}, 支付订单ID：{}", originalOrderId, orderNum);
                Long orderId = Long.valueOf(originalOrderId);
                
                // 移除缓存关系
                RedisUtils.deleteObject(cacheKey);
                
                // 获取订单和支付信息
                OrderMain orderMainDB = orderMainService.getById(orderId);
                OrderPayment orderPaymentDB = orderPaymentService.getById(orderId);
                
                if (orderMainDB != null && orderMainDB.getRunnerId() != null) {
                    // 查询是否有申诉记录
                    List<OrderAppeal> appeals = orderAppealMapper.selectList(
                        new LambdaQueryWrapper<OrderAppeal>()
                            .eq(OrderAppeal::getOrderId, originalOrderId)
                            .eq(OrderAppeal::getAppealTarget, 1) // 骑手申诉
                            .eq(OrderAppeal::getAppealStatus, Status.OK.getCode())
                            .orderByDesc(OrderAppeal::getUpdateTime)
                    );
                    
                    if (!appeals.isEmpty()) {
                        OrderAppeal appeal = appeals.get(0);
                        BigDecimal refundAmount = appeal.getRefundAmount();
                        
                        // 获取补差价金额，但不直接转入骑手账户，而是等管理员确认后再处理
                        if (refundAmount != null && refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                            // 不再直接计算并转入骑手账户，改为仅记录申诉状态和发送通知
                            
                            // 通知骑手补差价已支付成功
                            User runner = userService.selectUserByUid(orderMainDB.getRunnerId());
                            if (runner != null && runner.getUserWx() != null) {
                                List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildPriceAdjustmentData(
                                    String.valueOf(orderId),
                                    "补差价已支付成功",
                                    refundAmount.toString()
                                );
                                wxHelper.sendSubMsg(
                                    msgData,
                                    WxHelper.PAGE_ORDER_DETAIL + "?id=" + orderId,
                                    WxHelper.TEMPLATE_PRICE_ADJUSTMENT,
                                    runner.getUserWx().getOpenid()
                                );
                            }
                            
                            // 更新申诉状态，标记为已支付补差价，但需要管理员确认完成
                            appeal.setRemarks("补差价已支付完成，等待管理员确认订单完成");
                            orderAppealMapper.updateById(appeal);
                            
                            // 确保订单状态保持为"已申诉"(11)状态，等待管理员手动确认完成
                            if (orderMainDB.getStatus() != OrderStatus.CLOSED.getCode()) {
                                orderMainDB.setStatus(OrderStatus.CLOSED.getCode());
                                orderMainService.updateById(orderMainDB);
                                log.info("修正订单状态为已申诉(11)，等待管理员确认，订单ID: {}", orderId);
                            }
                            
                            log.info("骑手补差价已支付成功，等待管理员确认后转入骑手账户，订单ID: {}, 骑手ID: {}, 补差价金额: {}", 
                                    orderId, orderMainDB.getRunnerId(), refundAmount);
                        }
                    }
                }
                return; // 补差价处理完成，不继续执行普通支付逻辑
            }
            
            // 普通支付处理逻辑
            Long orderId = Long.valueOf(orderNum);
            log.info("订单支付成功，订单ID为：{}", orderId);
            
            // 查询订单信息
            OrderMain orderMainDB = orderMainService.getById(orderId);
            if (orderMainDB == null) {
                log.error("支付成功处理失败：未找到订单，订单ID: {}", orderId);
                return;
            }
            
            // 查询支付信息
            OrderPayment orderPaymentDB = orderPaymentService.getById(orderId);
            if (orderPaymentDB == null) {
                log.error("支付成功处理失败：未找到支付记录，订单ID: {}", orderId);
                return;
            }
            
            // 检查订单状态，避免重复处理
            if (OrderStatus.PENDING_ACCEPTANCE.getCode().equals(orderMainDB.getStatus()) && 
                PayStatus.PAID.getCode().equals(orderPaymentDB.getPaymentStatus())) {
                log.info("订单已经是待接单状态，无需重复更新，订单ID: {}", orderId);
                return;
            }
            
            //更新基本订单状态为 待接单
            orderMainDB.setStatus(OrderStatus.PENDING_ACCEPTANCE.getCode());
            boolean updateOrderResult = orderMainService.updateById(orderMainDB);
            log.info("更新订单状态结果: {}, 订单ID: {}", updateOrderResult, orderId);
            
            //更新订单支付表
            orderPaymentDB.setPaymentStatus(PayStatus.PAID.getCode());
            // 使用中国标准时区设置支付时间，确保时间准确
            orderPaymentDB.setPaymentTime(TimeZoneUtils.getNowInChina());
            boolean updatePaymentResult = orderPaymentService.updateById(orderPaymentDB);
            log.info("更新支付状态结果: {}, 订单ID: {}, 支付时间: {}", updatePaymentResult, orderId, TimeZoneUtils.formatDateTime(orderPaymentDB.getPaymentTime()));

            // 记录资金流动 - 订单支付
            try {
                CapitalFlow capitalFlow = new CapitalFlow();
                capitalFlow.setOrderId(orderId);
                capitalFlow.setUserId(orderMainDB.getUserId());
                capitalFlow.setRunnerId(null); // 支付时还没有跑腿员
                capitalFlow.setProfitPlat(BigDecimal.ZERO); // 支付时平台还没有收益
                capitalFlow.setProfitAgent(BigDecimal.ZERO);
                capitalFlow.setProfitRunner(BigDecimal.ZERO);
                // 用户支出记为负值
                capitalFlow.setProfitUser(orderPaymentDB.getActualPayment().negate());
                capitalFlow.setType(CapitalType.ORDER_PAYMENT.getCode());
                capitalFlow.setCreateTime(TimeZoneUtils.getNowInChina());
                capitalFlowMapper.insert(capitalFlow);
                log.info("订单支付资金流动记录成功，订单ID: {}, 支付金额: {}", orderId, orderPaymentDB.getActualPayment());
            } catch (Exception e) {
                log.error("记录订单支付资金流动失败，订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
                // 继续执行，不影响订单流程
            }

            //添加到延迟队列
            Integer autoCancelTtl = orderMainDB.getAutoCancelTtl();
            QueueUtils.addDelayedQueueObject(
                    QueueNames.ORDER_PENDING_ACCEPT_CANCEL,
                    orderId, autoCancelTtl,
                    TimeUnit.SECONDS);
            log.info("已将订单添加到自动取消延迟队列，订单ID: {}, 延迟时间: {}秒", orderId, autoCancelTtl);

            //记录活跃用户
            RedisUtils.recordAU(
                    LocalDate.now().toString(),
                    orderMainDB.getUserId().toString(),
                    CacheNames.DAILY_AU_USER
            );
            log.info("订单支付成功处理完成，订单ID: {}", orderId);
        }
        catch (Exception e) {
            log.error("用户支付成功，但更新订单失败，订单号: {}, 错误: {}", orderNum, e.getMessage(), e);
            // 可以考虑将失败的订单记录到特定队列，以便后续重试
            try {
                String failKey = "payment_update_fail:" + orderNum;
                RedisUtils.setCacheObject(failKey, orderNum);
                log.info("已将更新失败的订单记录到Redis，键: {}", failKey);
            } catch (Exception ex) {
                log.error("记录失败订单到Redis异常", ex);
            }
        }
    }
    public void refundSuccess(String orderNum) {
        Long orderId = Long.valueOf(orderNum);
        log.info("事件监听，订单id为：{}",orderId);
        try {
            OrderMain orderMainDB = orderMainService.getById(orderId);
            //更新订单支付表
            OrderPayment orderPaymentDB = orderPaymentService.getById(orderId);
            orderPaymentDB.setPaymentStatus(PayStatus.REFUNDED.getCode());
            orderPaymentDB.setRefundTime(TimeZoneUtils.getNowInChina());
            orderPaymentService.updateById(orderPaymentDB);
            log.info("更新退款状态成功，订单ID: {}, 退款时间: {}", orderId, TimeZoneUtils.formatDateTime(orderPaymentDB.getRefundTime()));

            //通知用户
            List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildOrderStatusData(
                    orderId,
                    orderMainDB.getTag(),
                    "退款已到账",
                    "退款已返回零钱，请注意查看"
            );
            User user = userService.selectUserByUid(orderMainDB.getUserId());
            wxHelper.sendSubMsg(
                    msgData,
                    WxHelper.PAGE_ORDER_DETAIL+"?id="+orderId,
                    WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                    user.getUserWx().getOpenid()
            );
        }
        catch (Exception e) {
            log.error("用户退款成功，但更新订单失败，{}",e.getMessage());
        }
    }

}
