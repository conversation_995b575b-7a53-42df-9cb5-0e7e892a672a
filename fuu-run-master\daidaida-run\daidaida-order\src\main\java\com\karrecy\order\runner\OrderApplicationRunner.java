package com.karrecy.order.runner;

import com.karrecy.common.constant.QueueNames;
import com.karrecy.common.utils.redis.QueueUtils;
import com.karrecy.order.listener.OrderListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 初始化订单模块相关业务数据
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OrderApplicationRunner implements ApplicationRunner {

    private final OrderListener orderListener;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            // 初始化订单相关的延迟队列监听器
            initOrderDelayedQueueListeners();
            log.info("订单延迟队列监听器初始化成功");
        } catch (Exception e) {
            log.error("订单延迟队列监听器初始化失败", e);
            throw e;
        }
    }

    /**
     * 初始化订单相关的延迟队列监听器
     */
    private void initOrderDelayedQueueListeners() {
        // 延迟队列监听器已在DelayedQueueConfig中统一管理
        // 这里不再重复启动，避免冲突
        log.info("订单延迟队列监听器由DelayedQueueConfig统一管理，OrderApplicationRunner不再重复启动");

        // 可以在这里添加其他订单相关的延迟队列监听器
        // 例如：订单超时取消、支付超时等
    }
}
