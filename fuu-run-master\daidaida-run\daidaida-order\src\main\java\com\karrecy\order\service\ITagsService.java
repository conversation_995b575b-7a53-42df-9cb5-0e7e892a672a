package com.karrecy.order.service;

import com.karrecy.order.domain.po.Tags;
import com.baomidou.mybatisplus.extension.service.IService;
import java.math.BigDecimal;

/**
 * <p>
 * tag表 服务类
 * </p>
 */
public interface ITagsService extends IService<Tags> {

    /**
     * 根据标签名称、学校ID和服务类型查询标签信息
     * 
     * @param tagName 标签名称
     * @param schoolId 学校ID
     * @param serviceType 服务类型
     * @return 标签信息
     */
    Tags getTagByNameAndSchool(String tagName, Long schoolId, Integer serviceType);
    
    /**
     * 计算多个标签的总金额
     * 支持拆分逗号分隔的多个标签，并计算总金额
     * 
     * @param combinedTagNames 以逗号分隔的标签名称字符串
     * @param schoolId 学校ID
     * @param serviceType 服务类型
     * @return 多个标签的总金额
     */
    BigDecimal calculateMultiTagsAmount(String combinedTagNames, Long schoolId, Integer serviceType);
}
