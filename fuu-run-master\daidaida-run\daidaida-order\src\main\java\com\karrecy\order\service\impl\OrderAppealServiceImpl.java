package com.karrecy.order.service.impl;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.karrecy.common.config.DaidaidaConfig;
import com.karrecy.common.core.domain.entity.UserWx;
import com.karrecy.common.core.domain.model.LoginUser;
import com.karrecy.common.constant.UserConstants;
import com.karrecy.common.enums.*;
import com.karrecy.common.exception.OrderException;
import com.karrecy.common.helper.LoginHelper;
import com.karrecy.common.utils.wx.WxHelper;
import com.karrecy.order.domain.dto.OrderAppealDTO;
import com.karrecy.order.domain.po.OrderAppeal;
import com.karrecy.order.domain.po.OrderAttachment;
import com.karrecy.order.domain.po.OrderMain;
import com.karrecy.order.domain.po.OrderPayment;
import com.karrecy.order.domain.po.OrderProgress;
import com.karrecy.order.domain.po.School;
import com.karrecy.order.domain.vo.OrderAppealVO;
import com.karrecy.order.mapper.OrderAppealMapper;
import com.karrecy.order.mapper.OrderAttachmentMapper;
import com.karrecy.order.mapper.OrderMainMapper;
import com.karrecy.order.mapper.OrderPaymentMapper;
import com.karrecy.order.mapper.OrderProgressMapper;
import com.karrecy.order.mapper.SchoolMapper;
import com.karrecy.order.service.IOrderAppealService;
import com.karrecy.payment.domain.vo.PayedVO;
import com.karrecy.payment.service.IPayService;
import com.karrecy.payment.domain.po.CapitalFlow;
import com.karrecy.payment.mapper.CapitalFlowMapper;
import com.karrecy.system.domain.po.Oss;
import com.karrecy.system.domain.po.Wallet;
import com.karrecy.system.mapper.OssMapper;
import com.karrecy.system.mapper.UserWxMapper;
import com.karrecy.system.mapper.WalletMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.karrecy.common.utils.redis.RedisUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.time.Duration;

/**
 * <p>
 *  服务实现类
 * </p>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderAppealServiceImpl extends ServiceImpl<OrderAppealMapper, OrderAppeal> implements IOrderAppealService {


    private final OrderAppealMapper orderAppealMapper;
    private final OrderMainMapper orderMainMapper;
    private final OrderPaymentMapper orderPaymentMapper;
    private final OrderAttachmentMapper orderAttachmentMapper;
    private final OrderProgressMapper orderProgressMapper;
    private final UserWxMapper userWxMapper;
    private final OssMapper ossMapper;
    private final WalletMapper walletMapper;
    private final SchoolMapper schoolMapper;
    private final CapitalFlowMapper capitalFlowMapper;

    private final IPayService payService;
    private final WxHelper wxHelper;
    private final DaidaidaConfig daidaidaConfig;

    /**
     * 提交申诉
     * @param orderAppealDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(OrderAppealDTO orderAppealDTO) {
        //判断用户权限
        Long uid = LoginHelper.getUserId();
        OrderMain orderMainDB = orderMainMapper.selectById(orderAppealDTO.getOrderId());
        if (orderMainDB == null) {
            throw new OrderException("订单不存在");
        }
        
        // 判断申诉权限（用户只能申诉自己的订单，骑手只能申诉自己接的单）
        int appealTarget = orderAppealDTO.getAppealTarget() != null ? orderAppealDTO.getAppealTarget() : 0;
        if (appealTarget == 0 && !ObjectUtil.equals(orderMainDB.getUserId(), uid)) {
            throw new OrderException("您无权申诉此订单");
        }
        if (appealTarget == 1 && !ObjectUtil.equals(orderMainDB.getRunnerId(), uid)) {
            throw new OrderException("您无权申诉此订单");
        }
        
        //是否有审核中 或者 审核通过的 存在就return
        List<OrderAppeal> orderAppealsDB = orderAppealMapper.selectList(new LambdaQueryWrapper<OrderAppeal>()
                .eq(OrderAppeal::getOrderId,orderAppealDTO.getOrderId())
                .in(OrderAppeal::getAppealStatus,Status.PENDING.getCode(),Status.OK.getCode()));
        if (ObjectUtil.isNotEmpty(orderAppealsDB)) {
            throw new OrderException("已存在申诉，不可重复提交");
        }
        
        OrderAppeal orderAppeal = new OrderAppeal();
        orderAppeal.setOrderId(String.valueOf(orderAppealDTO.getOrderId()));
        orderAppeal.setSchoolId(orderMainDB.getSchoolId());
        orderAppeal.setAppealReason(orderAppealDTO.getAppealReason());
        orderAppeal.setAppealStatus(Status.PENDING.getCode());
        orderAppeal.setAppealTime(LocalDateTime.now());
        orderAppeal.setUpdateId(uid);
        orderAppeal.setUpdateTime(LocalDateTime.now());
        orderAppeal.setUpdateType(LoginHelper.getUserType().getCode());
        orderAppeal.setAppealTarget(appealTarget);
        orderAppeal.setRefundAmount(orderAppealDTO.getRefundAmount());
        
        orderAppealMapper.insert(orderAppeal);
        
        if (ObjectUtil.isNotEmpty(orderAppealDTO.getOssAppealList())) {
            List<Oss> ossesDB = ossMapper.selectBatchIds(orderAppealDTO.getOssAppealList());
            for (Oss oss : ossesDB) {
                OrderAttachment orderAttachment = new OrderAttachment();
                orderAttachment.setOrderId(orderAppealDTO.getOrderId());
                orderAttachment.setFileType(oss.getFileSuffix());
                orderAttachment.setFileName(oss.getFileName());
                orderAttachment.setFileSize(oss.getFileSize());
                orderAttachment.setFileUrl(oss.getUrl());
                orderAttachment.setType(oss.getType());
                orderAttachmentMapper.insert(orderAttachment);
            }
        }

        orderMainDB.setStatus(OrderStatus.CLOSED.getCode());
        orderMainMapper.updateById(orderMainDB);
    }

    /**
     * 根据orderId查询申诉
     * @param orderId
     * @return
     */
    @Override
    public List<OrderAppealVO> getListByOrderId(Long orderId) {
        //校验用户权限
        Long uid = LoginHelper.getUserId();
        OrderMain orderMainDB = orderMainMapper.selectById(orderId);
        if (ObjectUtil.isNull(orderMainDB)) {
            throw new OrderException("订单不存在");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser.getDeviceType().equals(DeviceType.WX.getCode())) {
            if (!ObjectUtil.equals(orderMainDB.getUserId(),uid) && !ObjectUtil.equals(orderMainDB.getRunnerId(),uid)){
                throw new OrderException("没有该权限");
            }
        }

        List<OrderAppealVO> orderAppealVOS = new ArrayList<>();
        List<OrderAppeal> orderAppealsDB = orderAppealMapper.selectList(new LambdaQueryWrapper<OrderAppeal>()
                .eq(OrderAppeal::getOrderId, orderId)
                .orderByDesc(OrderAppeal::getAppealTime));
        for (OrderAppeal orderAppeal : orderAppealsDB) {
            List<OrderAttachment> orderAttachmentsDB = orderAttachmentMapper.selectList(new LambdaQueryWrapper<OrderAttachment>().
                    eq(OrderAttachment::getOrderId, orderAppeal.getOrderId())
                    .eq(OrderAttachment::getType, FileType.APPEAL_IMAGES.getCode()));
            OrderAppealVO orderAppealVO = new OrderAppealVO();
            orderAppealVO.setOrderAppeal(orderAppeal);
            List<String> urls = orderAttachmentsDB.stream()
                    .map(OrderAttachment::getFileUrl)
                    .collect(Collectors.toList());
            orderAppealVO.setImageUrls(urls);
            orderAppealVOS.add(orderAppealVO);
        }

        return orderAppealVOS;
    }

    /**
     * 处理申诉
     * @param orderAppeal
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handle(OrderAppeal orderAppeal) {
        String orderId = orderAppeal.getOrderId();
        OrderMain orderMainDB = orderMainMapper.selectById(orderId);
        OrderPayment orderPaymentDB = orderPaymentMapper.selectById(orderId);
        
        // 获取申诉对象和退款金额
        Integer appealTarget = orderAppeal.getAppealTarget();
        BigDecimal refundAmount = orderAppeal.getRefundAmount();
        
        if (orderAppeal.getAppealStatus().equals(Status.OK.getCode())) {
            // 判断申诉对象类型
            if (appealTarget == null || appealTarget == 0) {
                // 退款给用户
                if (refundAmount != null && refundAmount.compareTo(BigDecimal.ZERO) > 0 && 
                    orderPaymentDB != null && orderPaymentDB.getActualPayment() != null && 
                    orderPaymentDB.getActualPayment().compareTo(BigDecimal.ZERO) > 0) {
                    
                    // 判断是否全额退款
                    if (refundAmount.compareTo(orderPaymentDB.getActualPayment()) >= 0) {
                        // 全额退款
                        payService.refund(Long.valueOf(orderId), orderPaymentDB.getActualPayment(), orderPaymentDB.getActualPayment());
                    } else {
                        // 部分退款
                        payService.refund(Long.valueOf(orderId), refundAmount, orderPaymentDB.getActualPayment());
                    }
                    
                    //通知用户
                    List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildOrderStatusData(
                            Long.valueOf(orderId),
                            orderMainDB.getTag(),
                            "申诉已通过",
                            "退款金额: " + refundAmount + "元"
                    );
                    UserWx orderUser = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>().eq(UserWx::getUid, orderMainDB.getUserId()));
                    wxHelper.sendSubMsg(
                            msgData,
                            WxHelper.PAGE_ORDER_DETAIL+"?id="+orderId,
                            WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                            orderUser.getOpenid());
                    
                    // 更新支付状态
                    orderPaymentDB.setPaymentStatus(PayStatus.REFUND_IN_PROGRESS.getCode());
                    orderPaymentDB.setRefundPendingTime(LocalDateTime.now());
                    orderPaymentMapper.updateById(orderPaymentDB);
                    
                    // 扣除跑腿员信用分
                    if (orderMainDB.getRunnerId() != null) {
                        UserWx runner = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>().eq(UserWx::getUid, orderMainDB.getRunnerId()));
                        if (ObjectUtil.isNotEmpty(runner)) {
                            runner.setCreditScore(runner.getCreditScore() - daidaidaConfig.getCreditDeduction());
                            userWxMapper.updateById(runner);
                        }
                    }
                } else {
                    // 金额为0或支付记录不存在，仅发送通知
                    List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildOrderStatusData(
                            Long.valueOf(orderId),
                            orderMainDB.getTag(),
                            "申诉已通过",
                            "申诉处理完成"
                    );
                    UserWx orderUser = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>().eq(UserWx::getUid, orderMainDB.getUserId()));
                    wxHelper.sendSubMsg(
                            msgData,
                            WxHelper.PAGE_ORDER_DETAIL+"?id="+orderId,
                            WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                            orderUser.getOpenid());
                }
            } else {
                // 骑手申诉补差价
                if (refundAmount != null && refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                    // 查询用户微信信息，准备发送支付通知
                    UserWx orderUser = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>().eq(UserWx::getUid, orderMainDB.getUserId()));
                    if (orderUser != null && orderUser.getOpenid() != null) {
                        try {
                            // 构建补差价的支付描述
                            String desc = "订单补差价-" + orderMainDB.getTag();
                            
                            // 创建补差价支付链接 - 使用独立的订单号
                            // 使用时间戳+原订单ID最后5位作为补差价订单标识
                            String originalId = orderId;
                            String lastFiveDigits = originalId.length() > 5 ? 
                                                   originalId.substring(originalId.length() - 5) : 
                                                   originalId;
                            Long extraOrderId = Long.valueOf(System.currentTimeMillis() + lastFiveDigits);
                            log.info("生成补差价订单号: {}, 原订单号: {}", extraOrderId, orderId);
                            PayedVO payInfo = payService.pay(desc, extraOrderId, refundAmount, orderUser.getOpenid());
                            
                            // 添加缓存记录原始订单ID和补差价订单ID的对应关系，用于支付回调时查找
                            RedisUtils.setCacheObject("extra_payment:" + extraOrderId, orderId, Duration.ofDays(3));
                            
                            // 更新订单状态为"已申诉"(11)，确保在生成支付链接后状态正确
                            orderMainDB.setStatus(OrderStatus.CLOSED.getCode());
                            orderMainMapper.updateById(orderMainDB);
                            log.info("更新订单状态为已申诉(11)，订单ID: {}", orderId);
                            
                            // 构建支付链接页面地址
                            String payPage = WxHelper.PAGE_ORDER_DETAIL + "?id=" + orderId + "&pay=1&type=extra";
                            
                            // 发送补差价通知给用户
                            List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildPriceAdjustmentData(
                                    orderId,
                                    orderMainDB.getTag(),
                                    refundAmount.toString()
                            );
                            wxHelper.sendSubMsg(
                                    msgData,
                                    payPage,
                                    WxHelper.TEMPLATE_PRICE_ADJUSTMENT,
                                    orderUser.getOpenid());
                            
                            // 通知骑手申诉已通过，等待用户支付
                            UserWx runnerUser = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>().eq(UserWx::getUid, orderMainDB.getRunnerId()));
                            if (runnerUser != null) {
                                List<WxMaSubscribeMessage.MsgData> runnerMsgData = wxHelper.buildOrderStatusData(
                                        Long.valueOf(orderId),
                                        orderMainDB.getTag(),
                                        "申诉已通过",
                                        "补差价金额: " + refundAmount + "元，等待用户支付"
                                );
                                wxHelper.sendSubMsg(
                                        runnerMsgData,
                                        WxHelper.PAGE_ORDER_DETAIL + "?id=" + orderId,
                                        WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                                        runnerUser.getOpenid());
                            }
                            
                            // 记录补差价支付请求
                            // 这里可以添加一条支付记录，标记为补差价类型
                            // 实际支付成功后会通过回调更新状态并给骑手账户增加金额
                        } catch (Exception e) {
                            log.error("创建补差价支付请求失败", e);
                            // 申诉失败回退状态
                            orderAppeal.setAppealStatus(Status.PENDING.getCode());
                            orderAppeal.setRemarks("创建补差价支付请求失败: " + e.getMessage());
                        }
                    } else {
                        // 用户微信信息不存在
                        orderAppeal.setAppealStatus(Status.DISABLE.getCode());
                        orderAppeal.setRemarks("用户微信账号不存在，无法发起支付");
                    }
                } else {
                    // 骑手申诉通过但金额为0，此时处理同用户申诉
                    List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildOrderStatusData(
                            Long.valueOf(orderId),
                            orderMainDB.getTag(),
                            "申诉已通过",
                            "申诉处理完成"
                    );
                    
                    // 通知骑手
                    UserWx runnerUser = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>().eq(UserWx::getUid, orderMainDB.getRunnerId()));
                    wxHelper.sendSubMsg(
                            msgData,
                            WxHelper.PAGE_ORDER_DETAIL+"?id="+orderId,
                            WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                            runnerUser.getOpenid());
                }
            }
        } else if (orderAppeal.getAppealStatus().equals(Status.DISABLE.getCode())) {
            //通知申诉方
            Long notifyUid = appealTarget != null && appealTarget == 1 ? orderMainDB.getRunnerId() : orderMainDB.getUserId();
            if (notifyUid != null) {
                UserWx notifyUser = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>().eq(UserWx::getUid, notifyUid));
                if (notifyUser != null) {
                    List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildOrderStatusData(
                            Long.valueOf(orderId),
                            orderMainDB.getTag(),
                            "申诉已驳回",
                            orderAppeal.getRemarks()
                    );
                    wxHelper.sendSubMsg(
                            msgData,
                            WxHelper.PAGE_ORDER_DETAIL+"?id="+orderId,
                            WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                            notifyUser.getOpenid());
                }
            }
        }
        
        orderAppeal.setUpdateTime(LocalDateTime.now());
        orderAppeal.setUpdateId(LoginHelper.getUserId());
        orderAppeal.setUpdateType(LoginHelper.getUserType().getCode());
        orderAppealMapper.updateById(orderAppeal);
    }
    
    /**
     * 确认补差价订单完成
     * 将已申诉且已支付补差价的订单状态改为已完成
     * 
     * @param orderId 订单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmExtraPaymentComplete(Long orderId) {
        // 查询订单信息
        OrderMain orderMain = orderMainMapper.selectById(orderId);
        if (orderMain == null) {
            throw new OrderException("订单不存在");
        }
        
        // 验证订单状态是否是已申诉
        if (orderMain.getStatus() != OrderStatus.CLOSED.getCode()) {
            throw new OrderException("只有已申诉的订单才能确认完成");
        }
        
        // 查询申诉记录
        List<OrderAppeal> appeals = orderAppealMapper.selectList(
            new LambdaQueryWrapper<OrderAppeal>()
                .eq(OrderAppeal::getOrderId, orderId)
                .eq(OrderAppeal::getAppealTarget, 1) // 骑手申诉
                .eq(OrderAppeal::getAppealStatus, Status.OK.getCode())
                .orderByDesc(OrderAppeal::getUpdateTime)
                .last("limit 1")
        );
        
        if (appeals.isEmpty()) {
            throw new OrderException("未找到有效的骑手申诉记录");
        }
        
        OrderAppeal appeal = appeals.get(0);
        
        // 验证申诉记录中的备注，确保已支付补差价
        if (appeal.getRemarks() == null || !appeal.getRemarks().contains("补差价已支付完成")) {
            throw new OrderException("该订单尚未完成补差价支付，无法确认完成");
        }
        
        // 更新订单状态为已完成
        orderMain.setStatus(OrderStatus.COMPLETED.getCode());
        orderMainMapper.updateById(orderMain);
        
        // 更新订单流程记录
        OrderProgress orderProgress = orderProgressMapper.selectById(orderId);
        if (orderProgress != null) {
            orderProgress.setCompletedTime(LocalDateTime.now());
            orderProgress.setCompletedType(UserType.SYSTEM.getCode()); // 系统确认完成
            orderProgressMapper.updateById(orderProgress);
        }
        
        // 更新申诉记录
        appeal.setRemarks(appeal.getRemarks() + "，管理员已确认订单完成");
        orderAppealMapper.updateById(appeal);
        
        // 订单收益分配
        // 查询订单支付信息
        OrderPayment orderPaymentDB = orderPaymentMapper.selectById(orderId);
        if (orderPaymentDB == null) {
            log.error("未找到订单支付信息，无法分配收益，订单ID: {}", orderId);
        } else {
            // 获取校区信息，用于计算分成比例
            School schoolDB = schoolMapper.selectById(orderMain.getSchoolId());
            if (schoolDB != null) {
                Integer profitPlat;
                Integer profitAgent = 0; // 取消代理收益
                Integer profitRunner; // 骑手收益占比
                
                // 获取订单总金额和追加金额
                BigDecimal totalAmount = orderMain.getTotalAmount();
                if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    log.error("订单总金额无效，无法分配收益，订单ID: {}, 总金额: {}", orderId, totalAmount);
                    totalAmount = BigDecimal.ONE; // 使用默认值避免后续计算错误
                }
                
                BigDecimal additionalAmount = orderPaymentDB.getAdditionalAmount();
                if (additionalAmount == null) {
                    additionalAmount = BigDecimal.ZERO;
                }

                // 查找可能存在的补差价支付记录
                List<CapitalFlow> extraPayments = capitalFlowMapper.selectList(
                    new LambdaQueryWrapper<CapitalFlow>()
                        .eq(CapitalFlow::getType, CapitalType.EXTRA_PAYMENT.getCode())
                        .eq(CapitalFlow::getOrderId, orderId)
                );

                // 补差价金额
                BigDecimal extraPaymentAmount = BigDecimal.ZERO;
                if (!extraPayments.isEmpty()) {
                    for (CapitalFlow extraPayment : extraPayments) {
                        // 补差价记录中用户支出为负值，取绝对值
                        if (extraPayment.getProfitUser() != null) {
                            extraPaymentAmount = extraPaymentAmount.add(extraPayment.getProfitUser().abs());
                        }
                    }
                    log.info("找到补差价支付记录，金额: {}", extraPaymentAmount);
                } else {
                    // 如果没有找到补差价支付记录，则从申诉记录中获取
                    extraPaymentAmount = appeal.getRefundAmount() != null ? appeal.getRefundAmount() : BigDecimal.ZERO;
                    log.info("使用申诉记录中的补差价金额: {}", extraPaymentAmount);
                }

                // 原订单实际支付金额
                BigDecimal originalPaymentAmount = orderPaymentDB.getActualPayment();
                if (originalPaymentAmount == null) {
                    originalPaymentAmount = BigDecimal.ZERO;
                }

                // 计算总收益金额 = 原订单金额 + 补差价金额
                BigDecimal totalProfitAmount = originalPaymentAmount.add(extraPaymentAmount);
                log.info("收益计算总金额 = 原订单金额({}) + 补差价金额({}) = {}", 
                         originalPaymentAmount, extraPaymentAmount, totalProfitAmount);

                // 使用总收益金额替换原来的totalAmount进行后续计算
                totalAmount = totalProfitAmount;

                // 判断是否为追加金额订单（标准是additionalAmount > 0且additionalAmount等于totalAmount）
                boolean isAdditionalOrder = additionalAmount.compareTo(BigDecimal.ZERO) > 0 && 
                                          orderMain.getTotalAmount().compareTo(additionalAmount) == 0;
                
                // 如果是追加金额订单且设置了专门的分成比例，则使用专门的分成比例
                if (isAdditionalOrder && schoolDB.getAdditionalProfitRate() != null) {
                    log.info("使用追加金额订单专门的平台分成比例: {}%", schoolDB.getAdditionalProfitRate());
                    profitPlat = schoolDB.getAdditionalProfitRate(); // 使用追加金额订单的平台分成比例
                    profitRunner = 100 - profitPlat; // 剩余部分给骑手
                } else {
                    // 使用默认分成比例
                    profitPlat = schoolDB.getProfitPlat() != null ? schoolDB.getProfitPlat() : 0;
                    Integer profitAgentPercentage = schoolDB.getProfitAgent() != null ? schoolDB.getProfitAgent() : 0;
                    profitPlat += profitAgentPercentage; // 平台收益占比(合并代理收益)
                    profitRunner = schoolDB.getProfitRunner() != null ? schoolDB.getProfitRunner() : 0; // 骑手收益占比
                }
                
                // 确保分成比例有效，避免除零错误
                if (profitPlat == null || profitPlat < 0) {
                    profitPlat = 0;
                }
                if (profitRunner == null || profitRunner < 0) {
                    profitRunner = 0;
                }
                
                Integer totalProfit = profitRunner + profitPlat;
                if (totalProfit <= 0) {
                    log.warn("总分成比例为零，使用默认分成比例 50:50，订单ID: {}", orderId);
                    profitPlat = 50;
                    profitRunner = 50;
                    totalProfit = 100;
                }
                
                // 计算收益
                BigDecimal profitPlatAmount;
                BigDecimal profitRunnerAmount;
                
                // 安全计算平台收益
                if (profitPlat > 0) {
                    profitPlatAmount = totalAmount
                            .multiply(new BigDecimal(profitPlat))
                            .divide(new BigDecimal(totalProfit), 2, RoundingMode.HALF_UP);
                } else {
                    profitPlatAmount = BigDecimal.ZERO;
                }
                
                // 代理收益设为零
                BigDecimal profitAgentAmount = BigDecimal.ZERO;
                
                // 安全计算骑手收益
                if (profitRunner > 0) {
                    profitRunnerAmount = totalAmount
                            .multiply(new BigDecimal(profitRunner))
                            .divide(new BigDecimal(totalProfit), 2, RoundingMode.HALF_UP);
                } else {
                    profitRunnerAmount = BigDecimal.ZERO;
                }
                
                // 查询钱包
                Long adminId = UserConstants.ADMIN_ID;
                Long runnerId = orderMain.getRunnerId();
                Long userId = orderMain.getUserId();
                
                if (runnerId == null) {
                    log.error("订单没有关联骑手，无法分配收益，订单ID: {}", orderId);
                    return;
                }
                
                try {
                    // 更新平台钱包
                    Wallet walletAdmin = walletMapper.selectById(adminId);
                    if (walletAdmin != null) {
                        walletAdmin.setBalance(walletAdmin.getBalance().add(profitPlatAmount));
                        walletAdmin.setUpdateTime(LocalDateTime.now());
                        walletMapper.updateById(walletAdmin);
                        log.info("更新平台钱包成功，增加余额: {}", profitPlatAmount);
                    } else {
                        log.error("未找到平台钱包，无法更新余额");
                    }
                    
                    // 更新骑手钱包
                    Wallet walletRunner = walletMapper.selectById(runnerId);
                    if (walletRunner != null) {
                        walletRunner.setBalance(walletRunner.getBalance().add(profitRunnerAmount));
                        walletRunner.setUpdateTime(LocalDateTime.now());
                        walletMapper.updateById(walletRunner);
                        log.info("更新骑手钱包成功，增加余额: {}", profitRunnerAmount);
                    } else {
                        log.error("未找到骑手钱包，无法更新余额");
                    }
                    
                    // 确保用户钱包存在（如果不存在则创建）
                    Wallet walletUser = walletMapper.selectById(userId);
                    if (walletUser == null) {
                        walletUser = new Wallet();
                        walletUser.setUid(userId);
                        walletUser.setBalance(BigDecimal.ZERO);
                        walletUser.setWithdrawn(BigDecimal.ZERO);
                        walletUser.setCreateTime(LocalDateTime.now());
                        walletUser.setUpdateTime(LocalDateTime.now());
                        walletMapper.insert(walletUser);
                        log.info("创建用户钱包成功，用户ID: {}", userId);
                    }
                    
                    // 获取实际支付金额，如果为空则使用订单总金额
                    BigDecimal actualPayment = orderPaymentDB.getActualPayment();
                    if (actualPayment == null || actualPayment.compareTo(BigDecimal.ZERO) <= 0) {
                        actualPayment = totalAmount;
                        log.warn("订单实际支付金额无效，使用订单总金额代替，订单ID: {}", orderId);
                    }
                    
                    // 插入资金流动记录
                    CapitalFlow capitalFlow = new CapitalFlow();
                    capitalFlow.setOrderId(orderId);
                    capitalFlow.setUserId(userId);
                    capitalFlow.setRunnerId(runnerId);
                    capitalFlow.setProfitPlat(profitPlatAmount);
                    capitalFlow.setProfitAgent(profitAgentAmount);
                    capitalFlow.setProfitRunner(profitRunnerAmount);
                    // 用户支出记为负值
                    capitalFlow.setProfitUser(totalProfitAmount.negate());
                    capitalFlow.setType(CapitalType.ORDER_COMPLETE.getCode());
                    capitalFlow.setCreateTime(LocalDateTime.now());
                    capitalFlowMapper.insert(capitalFlow);
                    log.info("订单收益分配完成。平台收益: {}, 骑手收益: {}, 订单总收益金额: {}", 
                            profitPlatAmount, profitRunnerAmount, totalProfitAmount);
                    
                    // 记录明细日志 - 用于审计和核对
                    log.info("订单 {} 收益计算明细 - 原订单金额: {}, 补差价金额: {}, 总收益: {}", 
                            orderId, originalPaymentAmount, extraPaymentAmount, totalProfitAmount);
                    
                    // 记录补差价处理方式的变更
                    if (extraPaymentAmount.compareTo(BigDecimal.ZERO) > 0) {
                        log.info("订单 {} 补差价处理: 补差价金额 {} 已在管理员确认完成时一次性计入骑手账户，不再在支付回调时处理", 
                                orderId, extraPaymentAmount);
                    }
                } catch (Exception e) {
                    log.error("订单收益分配失败", e);
                    throw new OrderException("订单收益分配失败: " + e.getMessage());
                }
            } else {
                log.error("未找到校区信息，无法计算分成比例，订单ID: {}, 校区ID: {}", 
                        orderId, orderMain.getSchoolId());
            }
        }
        
        // 发送消息通知给用户
        UserWx orderUser = userWxMapper.selectOne(
            new LambdaQueryWrapper<UserWx>()
                .eq(UserWx::getUid, orderMain.getUserId())
        );
        if (orderUser != null) {
            List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildOrderStatusData(
                Long.valueOf(orderId),
                orderMain.getTag(),
                OrderStatus.getString(OrderStatus.COMPLETED.getCode()),
                "补差价已支付，订单已完成"
            );
            wxHelper.sendSubMsg(
                msgData,
                WxHelper.PAGE_ORDER_DETAIL + "?id=" + orderId,
                WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                orderUser.getOpenid()
            );
        }
        
        // 发送消息通知给骑手
        UserWx runnerUser = userWxMapper.selectOne(
            new LambdaQueryWrapper<UserWx>()
                .eq(UserWx::getUid, orderMain.getRunnerId())
        );
        if (runnerUser != null) {
            List<WxMaSubscribeMessage.MsgData> msgData = wxHelper.buildOrderStatusData(
                Long.valueOf(orderId),
                orderMain.getTag(),
                OrderStatus.getString(OrderStatus.COMPLETED.getCode()),
                "补差价已确认，订单已完成"
            );
            wxHelper.sendSubMsg(
                msgData,
                WxHelper.PAGE_ORDER_DETAIL + "?id=" + orderId,
                WxHelper.TEMPLATE_ORDER_STATUS_CHANGE,
                runnerUser.getOpenid()
            );
        }
        
        log.info("管理员已确认补差价订单完成，订单ID: {}", orderId);
    }
}
