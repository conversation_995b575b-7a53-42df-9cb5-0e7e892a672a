package com.karrecy.order.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.karrecy.common.config.DaidaidaConfig;
import com.karrecy.common.constant.QueueNames;
import com.karrecy.common.enums.OrderStatus;
import com.karrecy.common.enums.PayStatus;
import com.karrecy.common.utils.TimeZoneUtils;
import com.karrecy.common.utils.redis.QueueUtils;
import com.karrecy.order.domain.po.OrderMain;
import com.karrecy.order.domain.po.OrderPayment;
import com.karrecy.order.domain.po.OrderProgress;
import com.karrecy.order.mapper.OrderMainMapper;
import com.karrecy.order.mapper.OrderPaymentMapper;
import com.karrecy.order.mapper.OrderProgressMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 订单状态检查定时任务
 * 用于修复可能存在的支付成功但订单状态未更新的情况
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderStatusCheckTask {

    private final OrderMainMapper orderMainMapper;
    private final OrderPaymentMapper orderPaymentMapper;
    private final OrderProgressMapper orderProgressMapper;
    private final DaidaidaConfig daidaidaConfig;

    /**
     * 每2分钟检查一次已支付但状态未更新的订单
     */
    @Scheduled(fixedDelay = 120000) // 2分钟执行一次
    @Transactional(rollbackFor = Exception.class)
    public void checkAndFixOrderStatus() {
        try {
            log.info("开始检查已支付但状态未更新的订单...时间: {}", TimeZoneUtils.getNowString());
            
            // 查询所有已支付但可能状态不正确的订单
            List<OrderPayment> paidOrders = orderPaymentMapper.selectList(
                new LambdaQueryWrapper<OrderPayment>()
                    .eq(OrderPayment::getPaymentStatus, PayStatus.PAID.getCode())
                    .isNotNull(OrderPayment::getPaymentTime)
                    .gt(OrderPayment::getPaymentTime, TimeZoneUtils.getNowInChina().minusDays(7)) // 检查最近7天的订单
            );
            
            log.info("找到已支付订单数量: {}", paidOrders.size());
            
            int fixedCount = 0;
            for (OrderPayment payment : paidOrders) {
                OrderMain order = orderMainMapper.selectById(payment.getOrderId());
                if (order != null && !OrderStatus.PENDING_ACCEPTANCE.getCode().equals(order.getStatus()) 
                        && !OrderStatus.PENDING_DELIVER.getCode().equals(order.getStatus())
                        && !OrderStatus.DELIVERING.getCode().equals(order.getStatus())
                        && !OrderStatus.DELIVERED.getCode().equals(order.getStatus())
                        && !OrderStatus.COMPLETED.getCode().equals(order.getStatus())
                        && !OrderStatus.CLOSED.getCode().equals(order.getStatus())) {
                    log.info("发现已支付但状态未更新的订单: {}, 当前状态: {}, 支付时间: {}", 
                            order.getId(), order.getStatus(), TimeZoneUtils.formatDateTime(payment.getPaymentTime()));
                    
                    // 排除已取消订单
                    if (order.getStatus() == OrderStatus.CLOSED.getCode() || 
                        order.getStatus() == OrderStatus.CANCELED.getCode()) {
                        log.info("订单{}已取消或关闭，不需要修复状态", order.getId());
                        continue;
                    }

                    // 排除有退款记录的订单
                    if (payment.getRefundPendingTime() != null) {
                        log.info("订单{}存在退款记录，不需要修复状态", order.getId());
                        continue;
                    }
                    
                    // 更新订单状态为待接单
                    order.setStatus(OrderStatus.PENDING_ACCEPTANCE.getCode());
                    int updateResult = orderMainMapper.updateById(order);
                    
                    log.info("已修复订单状态: {}, 更新为待接单状态, 更新结果: {}", order.getId(), updateResult > 0 ? "成功" : "失败");
                    fixedCount++;
                }
            }
            
            // 检查未支付但订单状态不对的情况
            List<OrderPayment> unpaidOrders = orderPaymentMapper.selectList(
                new LambdaQueryWrapper<OrderPayment>()
                    .eq(OrderPayment::getPaymentStatus, PayStatus.UNPAID.getCode())
                    .gt(OrderPayment::getOrderId, 0L)
            );
            
            int unpaidFixedCount = 0;
            for (OrderPayment payment : unpaidOrders) {
                OrderMain order = orderMainMapper.selectById(payment.getOrderId());
                if (order != null && !OrderStatus.PENDING_PAYMENT.getCode().equals(order.getStatus()) 
                        && !OrderStatus.CANCELED.getCode().equals(order.getStatus())
                        && !OrderStatus.CLOSED.getCode().equals(order.getStatus())) {
                    log.info("发现未支付但状态异常的订单: {}, 当前状态: {}", order.getId(), order.getStatus());
                    
                    // 更新订单状态为待支付
                    order.setStatus(OrderStatus.PENDING_PAYMENT.getCode());
                    int updateResult = orderMainMapper.updateById(order);
                    
                    log.info("已修复异常订单状态: {}, 更新为待支付状态, 更新结果: {}", order.getId(), updateResult > 0 ? "成功" : "失败");
                    unpaidFixedCount++;
                }
            }
            
            log.info("订单状态检查完成，共修复{}个已支付订单，{}个未支付订单", fixedCount, unpaidFixedCount);
        } catch (Exception e) {
            log.error("检查和修复订单状态异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 每30分钟检查一次超时未自动完成的已送达订单
     * 修复延迟队列可能丢失的情况
     */
    @Scheduled(fixedRate = 30 * 60 * 1000) // 30分钟
    @Transactional(rollbackFor = Exception.class)
    public void checkOverdueDeliveredOrders() {
        try {
            log.info("开始检查超时未自动完成的已送达订单");

            // 查询所有已送达状态的订单
            List<OrderMain> deliveredOrders = orderMainMapper.selectList(
                new LambdaQueryWrapper<OrderMain>()
                    .eq(OrderMain::getStatus, OrderStatus.DELIVERED.getCode())
            );

            if (deliveredOrders.isEmpty()) {
                log.info("没有已送达状态的订单需要检查");
                return;
            }

            int processedCount = 0;
            LocalDateTime now = LocalDateTime.now();
            long autoCompleteTtlHours = daidaidaConfig.getAutoCompleteTtl();

            for (OrderMain order : deliveredOrders) {
                try {
                    // 获取订单进度信息
                    OrderProgress progress = orderProgressMapper.selectById(order.getId());
                    if (progress != null && progress.getDeliveredTime() != null) {
                        // 计算送达时间到现在的小时数
                        long hoursFromDelivered = ChronoUnit.HOURS.between(progress.getDeliveredTime(), now);

                        // 如果超过配置的自动完成时间，重新加入延迟队列
                        if (hoursFromDelivered >= autoCompleteTtlHours) {
                            log.warn("发现超时未自动完成的订单，订单ID: {}, 送达时间: {}, 已过去{}小时",
                                    order.getId(), progress.getDeliveredTime(), hoursFromDelivered);

                            // 立即加入延迟队列执行自动完成
                            QueueUtils.addDelayedQueueObject(
                                QueueNames.ORDER_DELIVERED_AUTO_COMPLETE,
                                order.getId().toString(),
                                1,
                                TimeUnit.SECONDS
                            );

                            processedCount++;
                            log.info("已重新加入延迟队列，订单ID: {}", order.getId());
                        }
                    }
                } catch (Exception e) {
                    log.error("处理订单自动完成检查失败，订单ID: {}", order.getId(), e);
                }
            }

            log.info("超时订单检查任务完成，总共检查了 {} 个已送达订单，重新处理了 {} 个超时订单",
                    deliveredOrders.size(), processedCount);

        } catch (Exception e) {
            log.error("超时订单检查任务执行失败", e);
        }
    }
}