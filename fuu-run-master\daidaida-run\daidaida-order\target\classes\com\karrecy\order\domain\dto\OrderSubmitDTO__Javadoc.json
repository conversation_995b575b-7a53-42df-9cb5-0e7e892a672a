{"doc": "订单提交请求体\n", "fields": [{"name": "schoolId", "doc": " 学校id\n"}, {"name": "serviceType", "doc": " 服务类型 0 帮取送 1 代买 2 万能服务\n"}, {"name": "is<PERSON><PERSON>", "doc": " 是否加急订单 true:加急 false:普通\n"}, {"name": "tag", "doc": " 标签\n"}, {"name": "weight", "doc": " 物品重量\n"}, {"name": "detail", "doc": " 详情\n"}, {"name": "isTimed", "doc": " 是否指定时间 0 否 1 是\n"}, {"name": "specifiedTime", "doc": " 指定时间\n"}, {"name": "autoCancelTtl", "doc": " 未接单取消时间（秒）\n"}, {"name": "gender", "doc": " 0女 1男 2不限\n"}, {"name": "additionalAmount", "doc": " 追加金额\n"}, {"name": "estimatedPrice", "doc": " 预估商品价格\n"}, {"name": "attachImages", "doc": " 附件图片的ossIds\n"}, {"name": "attachFiles", "doc": " 附件文件的ossIds\n"}, {"name": "startAddress", "doc": " 起始地点\n"}, {"name": "end<PERSON>dd<PERSON>", "doc": " 终点\n"}], "enumConstants": [], "methods": [], "constructors": []}