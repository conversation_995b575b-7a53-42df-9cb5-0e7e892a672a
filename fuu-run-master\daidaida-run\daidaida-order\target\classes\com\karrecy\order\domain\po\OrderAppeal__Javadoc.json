{"doc": " 订单申诉类\n", "fields": [{"name": "orderId", "doc": " 订单id\n"}, {"name": "schoolId", "doc": " 学校id\n"}, {"name": "appealTime", "doc": " 申诉时间\n"}, {"name": "appealReason", "doc": " 申诉理由\n"}, {"name": "appealStatus", "doc": " 申诉状态 0 不通过 1 通过 2 申诉中\n"}, {"name": "updateTime", "doc": " 申诉更新时间\n"}, {"name": "remarks", "doc": " 申诉驳回原因\n"}, {"name": "updateId", "doc": " 更新人id\n"}, {"name": "updateType", "doc": " 更新人类型\n"}, {"name": "refundAmount", "doc": " 退款/补偿金额\n"}, {"name": "appeal<PERSON><PERSON>get", "doc": " 申诉对象类型 0 退款给用户 1 补差价给骑手\n"}], "enumConstants": [], "methods": [], "constructors": []}