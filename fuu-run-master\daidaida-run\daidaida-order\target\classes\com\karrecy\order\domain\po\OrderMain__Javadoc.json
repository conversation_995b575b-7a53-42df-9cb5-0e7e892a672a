{"doc": " 主订单类\n", "fields": [{"name": "schoolId", "doc": " 学校id\n"}, {"name": "serviceType", "doc": " 服务类型 0 帮取送 1 代买 2 万能服务\n"}, {"name": "tag", "doc": " 标签\n"}, {"name": "weight", "doc": " 物品重量\n"}, {"name": "startAddress", "doc": " 起点地址\n"}, {"name": "end<PERSON>dd<PERSON>", "doc": " 终点地址\n"}, {"name": "detail", "doc": " 具体描述（暴露）\n"}, {"name": "isTimed", "doc": " 是否指定时间 0 否 1 是\n"}, {"name": "specifiedTime", "doc": " 指定时间\n"}, {"name": "autoCancelTtl", "doc": " 未结单取消时间（秒）\n"}, {"name": "gender", "doc": " 0女 1男 2不限\n"}, {"name": "estimatedPrice", "doc": " 预估商品价格\n"}, {"name": "totalAmount", "doc": " 订单总金额\n"}, {"name": "status", "doc": " 订单状态\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "userId", "doc": " 用户id\n"}, {"name": "runnerId", "doc": " 跑腿id\n"}], "enumConstants": [], "methods": [], "constructors": []}