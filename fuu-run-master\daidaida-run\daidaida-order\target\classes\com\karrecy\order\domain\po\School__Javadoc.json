{"doc": " 校区类\n", "fields": [{"name": "belongUid", "doc": " 属于谁管理\n"}, {"name": "adcode", "doc": " 城市编码表\n"}, {"name": "name", "doc": " 学校名称\n"}, {"name": "logo", "doc": " 学校logo\n"}, {"name": "status", "doc": " 状态 0 禁用 1 启用\n"}, {"name": "profitPlat", "doc": " 平台收益占比\n"}, {"name": "profitAgent", "doc": " 代理收益占比\n"}, {"name": "profitRunner", "doc": " 跑腿收益占比\n"}, {"name": "floorPrice", "doc": " 追加金额底价\n"}, {"name": "emergencyMinAmount", "doc": " 加急订单最低追加金额\n"}, {"name": "additionalProfitRate", "doc": " 追加金额订单平台分成比例\n"}], "enumConstants": [], "methods": [], "constructors": []}