{"doc": " 订单大厅查询返回体\n", "fields": [{"name": "schoolId", "doc": " 学校id\n"}, {"name": "serviceType", "doc": " 服务类型 0 帮取送 1 代买 2 万能服务\n"}, {"name": "tag", "doc": " 标签\n"}, {"name": "startAddress", "doc": " 起点地址\n"}, {"name": "end<PERSON>dd<PERSON>", "doc": " 终点地址\n"}, {"name": "totalAmount", "doc": " 订单总金额\n"}, {"name": "status", "doc": " 订单状态\n"}, {"name": "createTime", "doc": " 创建时间\n"}], "enumConstants": [], "methods": [], "constructors": []}