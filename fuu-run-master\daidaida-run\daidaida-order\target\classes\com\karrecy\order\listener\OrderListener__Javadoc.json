{"doc": " @订单操作监听 解耦\n", "fields": [], "enumConstants": [], "methods": [{"name": "autoConfirmOrderBySystem", "paramTypes": ["java.lang.Long", "com.karrecy.order.domain.po.OrderMain"], "doc": " 系统自动确认订单完成（绕过权限检查）\n"}, {"name": "performProfitDistribution", "paramTypes": ["java.lang.Long", "com.karrecy.order.domain.po.OrderMain"], "doc": " 执行收益分发逻辑\n"}, {"name": "updateWalletBalances", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.math.BigDecimal", "java.math.BigDecimal"], "doc": " 更新钱包余额\n"}, {"name": "insertCapitalFlowRecord", "paramTypes": ["java.lang.Long", "com.karrecy.order.domain.po.OrderMain", "com.karrecy.order.domain.po.OrderPayment", "java.math.BigDecimal", "java.math.BigDecimal", "java.math.BigDecimal"], "doc": " 插入资金流动记录\n"}], "constructors": []}