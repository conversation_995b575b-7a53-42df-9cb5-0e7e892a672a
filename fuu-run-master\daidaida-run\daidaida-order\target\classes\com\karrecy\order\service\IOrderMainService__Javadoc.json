{"doc": " <p>\n  服务类\n </p>\n", "fields": [], "enumConstants": [], "methods": [{"name": "submit", "paramTypes": ["com.karrecy.order.domain.dto.OrderSubmitDTO"], "doc": " 新建订单\n\n @param orderSubmitDTO\n @return\n"}, {"name": "pageHall", "paramTypes": ["com.karrecy.order.domain.dto.OrderQuery", "com.karrecy.common.core.domain.PageQuery"], "doc": " 大厅订单查询\n @param orderQuery\n @param pageQuery\n @return\n"}, {"name": "pageUser", "paramTypes": ["com.karrecy.order.domain.dto.OrderQuery", "com.karrecy.common.core.domain.PageQuery"], "doc": " 我的订单查询\n @param orderQuery\n @param pageQuery\n @return\n"}, {"name": "getDetail", "paramTypes": ["java.lang.Long"], "doc": " 订单详情查询\n @param orderId\n @return\n"}, {"name": "payAgain", "paramTypes": ["java.lang.Long"], "doc": " 继续支付\n @param orderId\n @return\n"}, {"name": "cancel", "paramTypes": ["com.karrecy.order.domain.dto.OrderCancelDTO"], "doc": " 取消订单\n @param orderCancelDTO\n"}, {"name": "accept", "paramTypes": ["java.lang.Long"], "doc": " 跑腿员接单\n @param orderId\n"}, {"name": "updateImages", "paramTypes": ["com.karrecy.order.domain.dto.OrderCompleteDTO"], "doc": " 补充凭证\n @param orderCompleteDTO\n"}, {"name": "confirm", "paramTypes": ["java.lang.Long"], "doc": " 确定送达\n @param orderId\n"}, {"name": "autoConfirmBySystem", "paramTypes": ["java.lang.Long"], "doc": " 系统自动确认订单完成（绕过权限检查）\n @param orderId\n"}, {"name": "phone", "paramTypes": ["java.lang.Long"], "doc": "\n @param orderId\n @return\n"}, {"name": "pageChat", "paramTypes": ["com.karrecy.common.core.domain.PageQuery", "java.lang.Long"], "doc": " 订单聊天记录分页查询\n @param pageQuery\n @param orderId\n @return\n"}, {"name": "cancelbefore", "paramTypes": ["java.lang.Long"], "doc": " 取消订单前置操作\n @param orderId\n @return\n"}, {"name": "refund", "paramTypes": ["java.lang.Long", "java.math.BigDecimal"], "doc": " 订单退款\n @param orderId\n @param amount\n"}, {"name": "calculateOrderStatistics", "paramTypes": ["com.karrecy.common.core.domain.entity.StatisticsDaily", "java.time.LocalDate", "java.time.LocalDate"], "doc": " 统计订单数据\n @param statistics\n"}, {"name": "calculateFinancialStatistics", "paramTypes": ["com.karrecy.common.core.domain.entity.StatisticsDaily", "java.time.LocalDate", "java.time.LocalDate"], "doc": " 统计金额数据\n @param statistics\n"}], "constructors": []}