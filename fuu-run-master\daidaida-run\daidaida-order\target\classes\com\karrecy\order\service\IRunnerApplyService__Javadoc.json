{"doc": " <p>\n 跑腿申请表\n 服务类\n </p>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageRunnerApplyList", "paramTypes": ["com.karrecy.order.domain.po.RunnerApply", "com.karrecy.common.core.domain.PageQuery"], "doc": " 跑腿申请分页查询\n @param runnerApply\n @param pageQuery\n @return\n"}, {"name": "apply", "paramTypes": ["com.karrecy.order.domain.po.RunnerApply"], "doc": " 跑腿申请\n @param runnerApply\n"}, {"name": "handle", "paramTypes": ["com.karrecy.order.domain.po.RunnerApply"], "doc": " 处理申请\n @param runnerApply\n"}], "constructors": []}