{"doc": " <p>\n tag表 服务类\n </p>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getTagByNameAndSchool", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer"], "doc": " 根据标签名称、学校ID和服务类型查询标签信息\n \n @param tagName 标签名称\n @param schoolId 学校ID\n @param serviceType 服务类型\n @return 标签信息\n"}, {"name": "calculateMultiTagsAmount", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.Integer"], "doc": " 计算多个标签的总金额\n 支持拆分逗号分隔的多个标签，并计算总金额\n \n @param combinedTagNames 以逗号分隔的标签名称字符串\n @param schoolId 学校ID\n @param serviceType 服务类型\n @return 多个标签的总金额\n"}], "constructors": []}