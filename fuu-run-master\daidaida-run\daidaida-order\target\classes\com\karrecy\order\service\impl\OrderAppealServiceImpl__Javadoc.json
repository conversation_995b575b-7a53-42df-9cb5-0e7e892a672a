{"doc": " <p>\n  服务实现类\n </p>\n", "fields": [], "enumConstants": [], "methods": [{"name": "submit", "paramTypes": ["com.karrecy.order.domain.dto.OrderAppealDTO"], "doc": " 提交申诉\n @param orderAppealDTO\n"}, {"name": "getListByOrderId", "paramTypes": ["java.lang.Long"], "doc": " 根据orderId查询申诉\n @param orderId\n @return\n"}, {"name": "handle", "paramTypes": ["com.karrecy.order.domain.po.OrderAppeal"], "doc": " 处理申诉\n @param orderAppeal\n"}, {"name": "confirmExtraPaymentComplete", "paramTypes": ["java.lang.Long"], "doc": " 确认补差价订单完成\n 将已申诉且已支付补差价的订单状态改为已完成\n \n @param orderId 订单ID\n"}], "constructors": []}