{"doc": " <p>\n  服务实现类\n </p>\n", "fields": [], "enumConstants": [], "methods": [{"name": "submit", "paramTypes": ["com.karrecy.order.domain.dto.OrderSubmitDTO"], "doc": " 新建订单\n\n @param orderSubmitDTO\n @return\n"}, {"name": "getDetail", "paramTypes": ["java.lang.Long"], "doc": " 订单详情查询\n @param orderId\n @return\n"}, {"name": "payAgain", "paramTypes": ["java.lang.Long"], "doc": " 继续支付\n @param orderId\n @return\n"}, {"name": "cancel", "paramTypes": ["com.karrecy.order.domain.dto.OrderCancelDTO"], "doc": " 取消订单\n @param orderCancelDTO\n"}, {"name": "accept", "paramTypes": ["java.lang.Long"], "doc": " 跑腿员接单\n @param orderId\n"}, {"name": "delivery", "paramTypes": ["java.lang.Long"], "doc": " 跑腿员配送\n @param orderId\n"}, {"name": "complete", "paramTypes": ["com.karrecy.order.domain.dto.OrderCompleteDTO"], "doc": " 完成订单\n @param orderCompleteDTO\n"}, {"name": "updateImages", "paramTypes": ["com.karrecy.order.domain.dto.OrderCompleteDTO"], "doc": " 补充凭证\n @param orderCompleteDTO\n"}, {"name": "confirm", "paramTypes": ["java.lang.Long"], "doc": " 确定送达\n @param orderId\n"}, {"name": "phone", "paramTypes": ["java.lang.Long"], "doc": " 获取用户/跑腿员电话\n @param orderId\n @return\n"}, {"name": "pageChat", "paramTypes": ["com.karrecy.common.core.domain.PageQuery", "java.lang.Long"], "doc": " 订单聊天记录分页查询\n @param pageQuery\n @param orderId\n @return\n"}, {"name": "cancelbefore", "paramTypes": ["java.lang.Long"], "doc": " 取消订单前置操作\n @param orderId\n @return\n"}, {"name": "refund", "paramTypes": ["java.lang.Long", "java.math.BigDecimal"], "doc": " 订单退款\n @param orderId\n @param amount\n"}, {"name": "calculateOrderStatistics", "paramTypes": ["com.karrecy.common.core.domain.entity.StatisticsDaily", "java.time.LocalDate", "java.time.LocalDate"], "doc": " 统计订单数据\n @param statistics\n"}, {"name": "calculateFinancialStatistics", "paramTypes": ["com.karrecy.common.core.domain.entity.StatisticsDaily", "java.time.LocalDate", "java.time.LocalDate"], "doc": " 统计金额数据\n @param statistics\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "paramTypes": ["com.karrecy.order.domain.dto.OrderSubmitDTO"], "doc": " 检查字段是否合法\n @param orderSubmitDTO\n"}], "constructors": []}