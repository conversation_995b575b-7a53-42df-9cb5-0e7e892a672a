{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "getCancelFeeRate", "paramTypes": ["java.time.LocalDateTime"], "doc": " 根据取消时间计算扣费比例\n\n @param orderAcceptTime 接单时间\n @return 扣费比例（0.1 = 10%）\n"}, {"name": "calculateRefund", "paramTypes": ["java.math.BigDecimal", "java.time.LocalDateTime"], "doc": " 计算扣除费用\n\n @param totalPrice      订单总金额\n @param orderAcceptTime 接单时间\n @return 实际退款金额\n"}], "constructors": []}