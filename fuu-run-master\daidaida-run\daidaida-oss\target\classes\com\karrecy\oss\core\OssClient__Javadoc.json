{"doc": " S3 存储协议 所有兼容S3协议的云厂商均支持\n 阿里云 腾讯云 七牛云 minio\n", "fields": [], "enumConstants": [], "methods": [{"name": "getObjectMetadata", "paramTypes": ["java.lang.String"], "doc": " 获取文件元数据\n\n @param path 完整文件路径\n"}, {"name": "getPrivateUrl", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取私有URL链接\n\n @param objectKey 对象KEY\n @param second    授权时间\n"}, {"name": "checkPropertiesSame", "paramTypes": ["com.karrecy.oss.properties.OssProperties"], "doc": " 检查配置是否相同\n"}, {"name": "getAccessPolicy", "paramTypes": [], "doc": " 获取当前桶权限类型\n\n @return 当前桶权限类型code\n"}], "constructors": []}