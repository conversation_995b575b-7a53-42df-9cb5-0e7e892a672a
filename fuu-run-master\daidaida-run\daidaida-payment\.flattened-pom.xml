<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.karrecy</groupId>
    <artifactId>daidaida-run</artifactId>
    <version>1.0.0</version>
  </parent>
  <groupId>com.karrecy</groupId>
  <artifactId>daidaida-payment</artifactId>
  <version>1.0.0</version>
  <description>支付模块</description>
  <dependencies>
    <dependency>
      <groupId>com.karrecy</groupId>
      <artifactId>daidaida-common</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.github.wxpay</groupId>
      <artifactId>wxpay-sdk</artifactId>
      <version>0.0.3</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>com.karrecy</groupId>
      <artifactId>daidaida-system</artifactId>
      <version>${revision}</version>
    </dependency>
  </dependencies>
</project>
