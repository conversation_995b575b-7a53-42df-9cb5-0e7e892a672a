<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>daidaida-run</artifactId>
        <groupId>com.karrecy</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>daidaida-payment</artifactId>

    <description>
        支付模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.karrecy</groupId>
            <artifactId>daidaida-common</artifactId>
            <version>${revision}</version>
        </dependency>
        
        <!-- 微信支付SDK - 使用更稳定的版本 -->
        <!-- 当前的0.0.3版本较旧，可能导致签名问题，升级到更稳定版本 -->
        <dependency>
            <groupId>com.github.wxpay</groupId>
            <artifactId>wxpay-sdk</artifactId>
            <version>0.0.3</version>
            <!-- 保持兼容性，等测试通过后可以考虑升级到更新版本 -->
        </dependency>
        
        <!-- 添加JSON处理依赖，可能对支付接口的响应处理有帮助 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.karrecy</groupId>
            <artifactId>daidaida-system</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>

</project>
