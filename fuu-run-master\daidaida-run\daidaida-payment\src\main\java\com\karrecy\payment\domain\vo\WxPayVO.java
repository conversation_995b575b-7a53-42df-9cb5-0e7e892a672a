package com.karrecy.payment.domain.vo;

import lombok.Data;

/**
 * 微信支付返回参数视图对象
 */
@Data
public class WxPayVO {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 时间戳
     */
    private String timeStamp;

    /**
     * 随机字符串
     */
    private String nonceStr;

    /**
     * 订单详情扩展字符串
     */
    private String packageValue;

    /**
     * 签名方式
     */
    private String signType;

    /**
     * 签名
     */
    private String paySign;

    /**
     * 订单号
     */
    private String orderNo;

} 