package com.karrecy.payment.service;

import com.karrecy.payment.domain.dto.PayOrderDTO;
import com.karrecy.payment.domain.vo.WxPayVO;

import java.util.Map;

/**
 * 微信支付服务接口
 */
public interface WxPayService {

    /**
     * 创建JSAPI支付订单（小程序支付）
     *
     * @param orderDTO 支付订单数据
     * @param openId 用户openId
     * @param clientIp 客户端IP
     * @return 微信支付参数
     */
    WxPayVO createJsapiOrder(PayOrderDTO orderDTO, String openId, String clientIp);

    /**
     * 查询订单状态
     *
     * @param orderNo 订单号
     * @return 订单状态信息
     */
    Map<String, String> queryOrderStatus(String orderNo);

    /**
     * 申请退款
     *
     * @param orderNo 订单号
     * @param refundNo 退款单号
     * @param totalFee 订单总金额（单位：分）
     * @param refundFee 退款金额（单位：分）
     * @return 退款结果
     */
    Map<String, String> refund(String orderNo, String refundNo, Integer totalFee, Integer refundFee);
} 