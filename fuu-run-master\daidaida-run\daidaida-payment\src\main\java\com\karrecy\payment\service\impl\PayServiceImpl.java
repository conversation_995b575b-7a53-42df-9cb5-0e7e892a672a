package com.karrecy.payment.service.impl;

import com.github.wxpay.sdk.WXPayUtil;
import com.karrecy.common.constant.QueueNames;
import com.karrecy.common.exception.PayException;
import com.karrecy.common.utils.TimeZoneUtils;
import com.karrecy.common.utils.redis.QueueUtils;
import com.karrecy.common.utils.redis.RedisUtils;
import com.karrecy.payment.domain.dto.PayOrderDTO;
import com.karrecy.payment.domain.vo.PayedVO;
import com.karrecy.payment.domain.vo.WxPayVO;
import com.karrecy.payment.service.IPayService;
import com.karrecy.payment.service.WxPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.Map;
import java.util.UUID;

/**
 * 支付服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PayServiceImpl implements IPayService {

    private final WxPayService wxPayService;

    /**
     * 微信支付回调处理
     */
    @Override
    public String payNotifyV3(String notifyData, HttpServletRequest request) {
        try {
            log.info("收到微信支付回调: {}", notifyData);
            
            // 解析回调数据，获取订单号
            Map<String, String> resultMap = WXPayUtil.xmlToMap(notifyData);
            if ("SUCCESS".equals(resultMap.get("return_code")) && "SUCCESS".equals(resultMap.get("result_code"))) {
                // 获取商户订单号
                String orderNo = resultMap.get("out_trade_no");
                log.info("支付成功，订单号: {}", orderNo);
                
                // 将订单号添加到支付成功队列
                QueueUtils.addQueueObject(QueueNames.ORDER_PAY_SUCCESS, orderNo);
                log.info("已将订单号{}添加到支付成功队列", orderNo);
            } else {
                log.warn("微信支付回调返回非成功状态: return_code={}, result_code={}, return_msg={}", 
                        resultMap.get("return_code"), resultMap.get("result_code"), resultMap.get("return_msg"));
            }
            
            return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        } catch (Exception e) {
            log.error("处理微信支付回调异常", e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>";
        }
    }

    /**
     * 请求微信支付
     */
    @Override
    public PayedVO pay(String desc, Long orderId, BigDecimal totalAmount, String openid) {
        try {
            // 将金额转换为分（微信支付单位）
            int amountInCents = totalAmount.multiply(new BigDecimal(100)).intValue();
            
            // 确保支付金额至少为1分钱（微信支付的最低限额）
            if (amountInCents < 1) {
                amountInCents = 1;
                log.info("订单金额为0，已自动调整为最小支付金额1分钱，订单号: {}", orderId);
            }
            
            // 构建支付订单数据
            PayOrderDTO orderDTO = new PayOrderDTO();
            orderDTO.setOrderNo(orderId.toString());
            orderDTO.setSubject(desc);
            orderDTO.setAmount(amountInCents);
            
            // 获取客户端IP
            String clientIp = "127.0.0.1"; // 实际中应从请求中获取
            
            // 调用微信支付统一下单
            WxPayVO wxPayResult = wxPayService.createJsapiOrder(orderDTO, openid, clientIp);
            
            // 将结果转换为前端所需格式
            PayedVO result = new PayedVO();
            result.setOrderId(orderId);
            result.setAppId(wxPayResult.getAppId());
            result.setTimeStamp(wxPayResult.getTimeStamp());
            result.setNonceStr(wxPayResult.getNonceStr());
            result.setPackageValue(wxPayResult.getPackageValue());
            result.setSignType(wxPayResult.getSignType());
            result.setPaySign(wxPayResult.getPaySign());
            
            return result;
        } catch (Exception e) {
            log.error("微信支付下单异常", e);
            throw new PayException("微信支付下单失败: " + e.getMessage());
        }
    }

    /**
     * 申请退款
     */
    @Override
    public void refund(Long orderId, BigDecimal totalAmount, BigDecimal refundAmount) {
        try {
            // 将金额转换为分（微信支付单位）
            int totalFee = totalAmount.multiply(new BigDecimal(100)).intValue();
            int refundFee = refundAmount.multiply(new BigDecimal(100)).intValue();
            
            // 生成退款单号（这里简单地添加RF前缀）
            String refundNo = "RF" + orderId;
            
            // 调用微信支付退款接口
            Map<String, String> refundResult = wxPayService.refund(orderId.toString(), refundNo, totalFee, refundFee);
            
            // 检查退款结果
            if (!"SUCCESS".equals(refundResult.get("return_code")) || !"SUCCESS".equals(refundResult.get("result_code"))) {
                String errMsg = refundResult.get("err_code_des");
                if (errMsg == null || errMsg.isEmpty()) {
                    errMsg = refundResult.get("return_msg");
                }
                throw new PayException("微信退款失败: " + errMsg);
            }
            
            // 退款成功后可以更新订单状态等操作
            log.info("微信退款成功, 订单号: {}, 退款金额: {}", orderId, refundAmount);
        } catch (Exception e) {
            log.error("微信退款异常", e);
            throw new PayException("微信退款失败: " + e.getMessage());
        }
    }

    /**
     * 退款回调处理
     */
    @Override
    public String refundNotify(String notifyData, HttpServletRequest request) {
        try {
            log.info("收到微信退款回调: {}", notifyData);
            // 处理微信退款回调逻辑
            // 通常需要验证签名、更新退款状态等
            return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        } catch (Exception e) {
            log.error("处理微信退款回调异常", e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>";
        }
    }

    /**
     * 补差价支付
     * 专门处理已申诉订单的补差价支付，忽略订单状态检查
     */
    @Override
    public PayedVO payExtraAmount(String desc, Long orderId, BigDecimal extraAmount, String openid) {
        try {
            log.info("处理补差价支付请求: orderId={}, amount={}, openid={}", orderId, extraAmount, openid);
            
            // 生成一个新的唯一订单号，格式: EXTRA + 时间戳 + 原订单ID后5位
            String originalId = orderId.toString();
            String lastFiveDigits = originalId.length() > 5 ? 
                                   originalId.substring(originalId.length() - 5) : 
                                   originalId;
            Long extraOrderId = Long.valueOf("8" + System.currentTimeMillis() + lastFiveDigits);
            log.info("生成补差价订单号: {}, 原订单号: {}", extraOrderId, orderId);
            
            // 将金额转换为分（微信支付单位）
            int amountInCents = extraAmount.multiply(new BigDecimal(100)).intValue();
            
            // 确保支付金额至少为1分钱（微信支付的最低限额）
            if (amountInCents < 1) {
                amountInCents = 1;
                log.info("补差价金额过小，已自动调整为最小支付金额1分钱，订单号: {}", extraOrderId);
            }
            
            // 构建支付订单数据
            PayOrderDTO orderDTO = new PayOrderDTO();
            orderDTO.setOrderNo(extraOrderId.toString());
            orderDTO.setSubject(desc);
            orderDTO.setAmount(amountInCents);
            
            // 获取客户端IP
            String clientIp = "127.0.0.1"; // 实际中应从请求中获取
            
            // 调用微信支付统一下单
            WxPayVO wxPayResult = wxPayService.createJsapiOrder(orderDTO, openid, clientIp);
            
            // 将结果转换为前端所需格式
            PayedVO result = new PayedVO();
            result.setOrderId(extraOrderId); // 注意这里使用新生成的订单ID
            result.setAppId(wxPayResult.getAppId());
            result.setTimeStamp(wxPayResult.getTimeStamp());
            result.setNonceStr(wxPayResult.getNonceStr());
            result.setPackageValue(wxPayResult.getPackageValue());
            result.setSignType(wxPayResult.getSignType());
            result.setPaySign(wxPayResult.getPaySign());
            
            // 记录原订单ID和补差价订单ID的对应关系，用于支付回调时查找
            RedisUtils.setCacheObject("extra_payment:" + extraOrderId, orderId.toString(), Duration.ofDays(3));
            log.info("已记录补差价订单与原订单的关联关系: extraOrderId={}, orderId={}", extraOrderId, orderId);
            
            return result;
        } catch (Exception e) {
            log.error("补差价支付下单异常", e);
            throw new PayException("补差价支付下单失败: " + e.getMessage());
        }
    }
} 