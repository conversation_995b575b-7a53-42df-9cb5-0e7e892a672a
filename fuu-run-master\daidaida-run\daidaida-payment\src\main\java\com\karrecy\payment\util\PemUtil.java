package com.karrecy.payment.util;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

/**
 * PEM 格式密钥工具类
 */
public class PemUtil {
    
    /**
     * 从字符串加载私钥
     *
     * @param privateKeyPEM 私钥内容
     * @return 私钥对象
     */
    public static PrivateKey loadPrivateKeyFromString(String privateKeyPEM) throws Exception {
        // 清理私钥格式
        String cleanedKey = privateKeyPEM
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
                
        // 解码BASE64
        byte[] privateKeyBytes = Base64.getDecoder().decode(cleanedKey);
        
        // 加载PKCS8格式私钥
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        return keyFactory.generatePrivate(keySpec);
    }
} 