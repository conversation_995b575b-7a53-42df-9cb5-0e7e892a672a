{"groups": [{"name": "wx.pay", "type": "com.karrecy.payment.config.WxPayConfig", "sourceType": "com.karrecy.payment.config.WxPayConfig"}, {"name": "wx.pay", "type": "com.karrecy.payment.properties.WxPayProperties", "sourceType": "com.karrecy.payment.properties.WxPayProperties"}], "properties": [{"name": "wx.pay.app-id", "type": "java.lang.String", "description": "小程序或公众号AppID", "sourceType": "com.karrecy.payment.config.WxPayConfig"}, {"name": "wx.pay.app-id", "type": "java.lang.String", "description": "小程序或公众号AppID", "sourceType": "com.karrecy.payment.properties.WxPayProperties"}, {"name": "wx.pay.cert-path", "type": "java.lang.String", "description": "商户API证书路径", "sourceType": "com.karrecy.payment.config.WxPayConfig"}, {"name": "wx.pay.enable-fallback", "type": "java.lang.Bo<PERSON>an", "description": "是否启用降级策略", "sourceType": "com.karrecy.payment.config.WxPayConfig"}, {"name": "wx.pay.enable-fallback", "type": "java.lang.Bo<PERSON>an", "description": "是否启用降级策略", "sourceType": "com.karrecy.payment.properties.WxPayProperties"}, {"name": "wx.pay.key", "type": "java.lang.String", "description": "商户API密钥", "sourceType": "com.karrecy.payment.config.WxPayConfig"}, {"name": "wx.pay.key", "type": "java.lang.String", "description": "商户API密钥", "sourceType": "com.karrecy.payment.properties.WxPayProperties"}, {"name": "wx.pay.mch-id", "type": "java.lang.String", "description": "商户号", "sourceType": "com.karrecy.payment.config.WxPayConfig"}, {"name": "wx.pay.mch-id", "type": "java.lang.String", "description": "商户号", "sourceType": "com.karrecy.payment.properties.WxPayProperties"}, {"name": "wx.pay.merchant-serial-number", "type": "java.lang.String", "description": "商户API证书序列号 仅当usePublicKeyMode为false时需要", "sourceType": "com.karrecy.payment.properties.WxPayProperties"}, {"name": "wx.pay.refund-url", "type": "java.lang.String", "description": "退款回调地址", "sourceType": "com.karrecy.payment.config.WxPayConfig"}, {"name": "wx.pay.refund-url", "type": "java.lang.String", "description": "退款回调地址", "sourceType": "com.karrecy.payment.properties.WxPayProperties"}, {"name": "wx.pay.return-url", "type": "java.lang.String", "description": "回调地址", "sourceType": "com.karrecy.payment.config.WxPayConfig"}, {"name": "wx.pay.return-url", "type": "java.lang.String", "description": "回调地址", "sourceType": "com.karrecy.payment.properties.WxPayProperties"}, {"name": "wx.pay.use-public-key-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否使用公钥模式 设置为true时，使用微信支付公钥进行验签，不下载平台证书 设置为false时，使用平台证书模式", "sourceType": "com.karrecy.payment.properties.WxPayProperties"}], "hints": []}