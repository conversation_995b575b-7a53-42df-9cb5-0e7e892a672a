{"doc": " 微信支付配置类\n", "fields": [{"name": "appId", "doc": " 小程序或公众号AppID\n"}, {"name": "mchId", "doc": " 商户号\n"}, {"name": "key", "doc": " 商户API密钥\n"}, {"name": "returnUrl", "doc": " 回调地址\n"}, {"name": "refundUrl", "doc": " 退款回调地址\n"}, {"name": "enableFallback", "doc": " 是否启用降级策略\n"}, {"name": "UNIFIED_ORDER_URL", "doc": " 统一下单API\n"}, {"name": "ORDER_QUERY_URL", "doc": " 查询订单API\n"}, {"name": "CLOSE_ORDER_URL", "doc": " 关闭订单API\n"}, {"name": "REFUND_URL", "doc": " 申请退款API\n"}, {"name": "REFUND_QUERY_URL", "doc": " 查询退款API\n"}, {"name": "DOWNLOAD_BILL_URL", "doc": " 下载对账单\n"}, {"name": "DOWNLOAD_FUND_FLOW_URL", "doc": " 下载资金对账单\n"}, {"name": "REPORT_URL", "doc": " 交易保障\n"}, {"name": "SHORT_URL", "doc": " 转换短链接\n"}, {"name": "AUTH_CODE_TO_OPENID_URL", "doc": " 授权码查询openId接口\n"}, {"name": "certPath", "doc": " 商户API证书路径\n"}], "enumConstants": [], "methods": [], "constructors": []}