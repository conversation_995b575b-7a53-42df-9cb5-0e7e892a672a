{"doc": "\n 微信支付返回参数视图对象\r\n", "fields": [{"name": "appId", "doc": "\n 应用ID\r\n"}, {"name": "timeStamp", "doc": "\n 时间戳\r\n"}, {"name": "nonceStr", "doc": "\n 随机字符串\r\n"}, {"name": "packageValue", "doc": "\n 订单详情扩展字符串\r\n"}, {"name": "signType", "doc": "\n 签名方式\r\n"}, {"name": "paySign", "doc": "\n 签名\r\n"}, {"name": "orderNo", "doc": "\n 订单号\r\n"}], "enumConstants": [], "methods": [], "constructors": []}