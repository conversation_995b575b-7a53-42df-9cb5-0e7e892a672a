{"doc": " 微信支付属性配置类\n", "fields": [{"name": "appId", "doc": " 小程序或公众号AppID\n"}, {"name": "mchId", "doc": " 商户号\n"}, {"name": "key", "doc": " 商户API密钥\n"}, {"name": "returnUrl", "doc": " 回调地址\n"}, {"name": "refundUrl", "doc": " 退款回调地址\n"}, {"name": "enableFallback", "doc": " 是否启用降级策略\n"}, {"name": "usePublicKeyMode", "doc": " 是否使用公钥模式\n 设置为true时，使用微信支付公钥进行验签，不下载平台证书\n 设置为false时，使用平台证书模式\n"}, {"name": "merchantSerialNumber", "doc": " 商户API证书序列号\n 仅当usePublicKeyMode为false时需要\n"}], "enumConstants": [], "methods": [], "constructors": []}