{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "payNotifyV3", "paramTypes": ["java.lang.String", "javax.servlet.http.HttpServletRequest"], "doc": " 支付回调\n"}, {"name": "pay", "paramTypes": ["java.lang.String", "java.lang.Long", "java.math.BigDecimal", "java.lang.String"], "doc": " 请求微信支付\n @param desc\n @param orderId\n @param totalAmount\n @param openid\n @return\n"}, {"name": "refund", "paramTypes": ["java.lang.Long", "java.math.BigDecimal", "java.math.BigDecimal"], "doc": " 申请退款\n @param orderId\n @param totalAmount\n @param refundAmount\n"}, {"name": "refundNotify", "paramTypes": ["java.lang.String", "javax.servlet.http.HttpServletRequest"], "doc": " 退款成功回调\n @param notifyData\n @param request\n @return\n"}, {"name": "payExtraAmount", "paramTypes": ["java.lang.String", "java.lang.Long", "java.math.BigDecimal", "java.lang.String"], "doc": " 补差价支付\n 专门处理已申诉订单的补差价支付，忽略订单状态检查\n \n @param desc 支付描述\n @param orderId 原订单ID\n @param extraAmount 补差价金额\n @param openid 用户openid\n @return 支付参数\n"}], "constructors": []}