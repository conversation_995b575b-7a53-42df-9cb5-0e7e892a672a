{"doc": "\n 微信支付服务接口\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createJsapiOrder", "paramTypes": ["com.karrecy.payment.domain.dto.PayOrderDTO", "java.lang.String", "java.lang.String"], "doc": "\n 创建JSAPI支付订单（小程序支付）\r\n\r\n @param orderDTO 支付订单数据\r\n @param openId 用户openId\r\n @param clientIp 客户端IP\r\n @return 微信支付参数\r\n"}, {"name": "queryOrderStatus", "paramTypes": ["java.lang.String"], "doc": "\n 查询订单状态\r\n\r\n @param orderNo 订单号\r\n @return 订单状态信息\r\n"}, {"name": "refund", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 申请退款\r\n\r\n @param orderNo 订单号\r\n @param refundNo 退款单号\r\n @param totalFee 订单总金额（单位：分）\r\n @param refundFee 退款金额（单位：分）\r\n @return 退款结果\r\n"}], "constructors": []}