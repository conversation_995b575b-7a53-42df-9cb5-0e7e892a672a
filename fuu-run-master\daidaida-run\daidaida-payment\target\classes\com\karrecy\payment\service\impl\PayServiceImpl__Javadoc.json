{"doc": "\n 支付服务实现类\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "payNotifyV3", "paramTypes": ["java.lang.String", "javax.servlet.http.HttpServletRequest"], "doc": "\n 微信支付回调处理\r\n"}, {"name": "pay", "paramTypes": ["java.lang.String", "java.lang.Long", "java.math.BigDecimal", "java.lang.String"], "doc": "\n 请求微信支付\r\n"}, {"name": "refund", "paramTypes": ["java.lang.Long", "java.math.BigDecimal", "java.math.BigDecimal"], "doc": "\n 申请退款\r\n"}, {"name": "refundNotify", "paramTypes": ["java.lang.String", "javax.servlet.http.HttpServletRequest"], "doc": "\n 退款回调处理\r\n"}, {"name": "payExtraAmount", "paramTypes": ["java.lang.String", "java.lang.Long", "java.math.BigDecimal", "java.lang.String"], "doc": "\n 补差价支付\r\n 专门处理已申诉订单的补差价支付，忽略订单状态检查\r\n"}], "constructors": []}