{"doc": " 微信支付服务实现类\n", "fields": [], "enumConstants": [], "methods": [{"name": "createJsapiOrder", "paramTypes": ["com.karrecy.payment.domain.dto.PayOrderDTO", "java.lang.String", "java.lang.String"], "doc": " 统一下单接口 - JSAPI支付（小程序支付）\n"}, {"name": "queryOrderStatus", "paramTypes": ["java.lang.String"], "doc": " 查询订单状态\n"}, {"name": "refund", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": " 申请退款\n"}, {"name": "findCertificateFile", "paramTypes": [], "doc": " 在系统中查找证书文件\n @return 找到的证书路径，未找到则返回null\n"}, {"name": "copyFile", "paramTypes": ["java.io.File", "java.io.File"], "doc": " 复制文件\n"}, {"name": "postRequest", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 发送HTTP POST请求\n"}], "constructors": []}