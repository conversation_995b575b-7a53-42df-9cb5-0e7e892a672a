{"doc": " 对象存储配置对象 sys_oss_config\n", "fields": [{"name": "ossConfigId", "doc": " 主建\n"}, {"name": "config<PERSON><PERSON>", "doc": " 配置key\n"}, {"name": "accessKey", "doc": " accessKey\n"}, {"name": "secret<PERSON>ey", "doc": " 秘钥\n"}, {"name": "bucketName", "doc": " 桶名称\n"}, {"name": "prefix", "doc": " 前缀\n"}, {"name": "endpoint", "doc": " 访问站点\n"}, {"name": "domain", "doc": " 自定义域名\n"}, {"name": "isHttps", "doc": " 是否https（0否 1是）\n"}, {"name": "region", "doc": " 域\n"}, {"name": "status", "doc": " 是否默认（1=是,0=否）\n"}, {"name": "ext1", "doc": " 扩展字段\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "accessPolicy", "doc": " 桶权限类型(0private 1public 2custom)\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}], "enumConstants": [], "methods": [], "constructors": []}