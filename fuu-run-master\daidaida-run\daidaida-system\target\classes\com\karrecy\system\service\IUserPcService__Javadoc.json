{"doc": " <p>\n 平台管理员表 服务类\n </p>\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkUserNameUnique", "paramTypes": ["com.karrecy.common.core.domain.entity.UserPc"], "doc": " 校验用户名称是否唯一\n @param userPc\n @return\n"}, {"name": "checkPhoneUnique", "paramTypes": ["com.karrecy.common.core.domain.entity.UserPc"], "doc": " 校验手机号码是否唯一\n @param userPc\n @return\n"}, {"name": "deleteUserByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 删除用户\n @param userIds\n @return\n"}, {"name": "insertUser", "paramTypes": ["com.karrecy.common.core.domain.entity.User"], "doc": " 新增pc用户\n @param user\n @return\n"}, {"name": "updatePcUser", "paramTypes": ["com.karrecy.common.core.domain.entity.UserPc"], "doc": " 修改pc用户\n @param userPc\n @return\n"}, {"name": "updateWxUser", "paramTypes": ["com.karrecy.common.core.domain.entity.UserWx"], "doc": " 修改pc用户\n @param userWx\n @return\n"}, {"name": "getByUid", "paramTypes": ["java.lang.Long"], "doc": " 根据uid查询\n @param uid\n @return\n"}, {"name": "checkEmailUnique", "paramTypes": ["com.karrecy.common.core.domain.entity.UserPc"], "doc": " 检查邮箱是否唯一\n @param userPc\n @return\n"}], "constructors": []}