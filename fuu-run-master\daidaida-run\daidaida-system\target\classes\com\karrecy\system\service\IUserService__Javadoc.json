{"doc": " <p>\n 全局用户表 服务类\n </p>\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildUser", "paramTypes": ["java.lang.Integer", "com.karrecy.common.enums.DeviceType"], "doc": " 构建全局用户\n @return\n"}, {"name": "checkUserAllowed", "paramTypes": ["java.lang.Long"], "doc": " 判断当前用户是否可被操作\n @param uid\n"}, {"name": "selectUserByUid", "paramTypes": ["java.lang.Long"], "doc": " 根据uid查询全局用户\n @param uid\n @return\n"}, {"name": "bindPhone", "paramTypes": ["java.lang.String"], "doc": " 绑定手机号\n @param phoneCode\n"}, {"name": "canReqPhone", "paramTypes": ["java.lang.String"], "doc": " 获取手机号防刷\n @param key\n @return\n"}, {"name": "calculateUserStatistics", "paramTypes": ["com.karrecy.common.core.domain.entity.StatisticsDaily", "java.time.LocalDate", "java.time.LocalDate"], "doc": " 统计用户数据\n @param statistics\n @param lastDay\n @param today\n"}], "constructors": []}