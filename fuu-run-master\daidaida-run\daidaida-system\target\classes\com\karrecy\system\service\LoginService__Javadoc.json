{"doc": " 登录服务\n", "fields": [], "enumConstants": [], "methods": [{"name": "pcLogin", "paramTypes": ["com.karrecy.common.core.domain.model.LoginBody"], "doc": " PC端登录\n @param loginBody\n @return\n"}, {"name": "emailLogin", "paramTypes": ["com.karrecy.common.core.domain.model.EmailBody"], "doc": " PC端邮箱登录\n @param emailBody\n @return\n"}, {"name": "xcxLogin", "paramTypes": ["java.lang.String"], "doc": " 小程序登录\n @param xcxCode\n @return\n"}], "constructors": []}